#include <QApplication>
#include <QMainWindow>
#include <QPushButton>
#include <QVBoxLayout>
#include <QWidget>
#include <QMessageBox>
#include "CANLogModel.h"
#include "Parser.h"
#include "TraceWindow.h"
#include "FixedPositionTraceWindow.h"

class MainWindow : public QMainWindow
{
    Q_OBJECT
public:
    MainWindow(const QString &ascPath, const QString &dbcPath, QWidget *parent = nullptr)
        : QMainWindow(parent), ascFilePath(ascPath), dbcFilePath(dbcPath)
    {
        setupUI();
    }

private slots:
    void openTraceWindow()
    {
        TraceWindow *traceWindow = new TraceWindow(ascFilePath, dbcFilePath, this);
        traceWindow->show();
    }
    
    void openFixedPositionTraceWindow()
    {
        FixedPositionTraceWindow *fixedTraceWindow = new FixedPositionTraceWindow(ascFilePath, dbcFilePath, this);
        fixedTraceWindow->show();
    }

private:
    void setupUI()
    {
        setWindowTitle("CAN Log Viewer");
        resize(400, 200);
        
        QWidget *centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        QVBoxLayout *layout = new QVBoxLayout(centralWidget);
        
        QPushButton *traceButton = new QPushButton("Chronological Trace", this);
        traceButton->setMinimumHeight(50);
        traceButton->setToolTip("Open chronological display mode - each new row is inserted below the previous row");
        connect(traceButton, &QPushButton::clicked, this, &MainWindow::openTraceWindow);
        
        QPushButton *fixedTraceButton = new QPushButton("Fixed Position Trace", this);
        fixedTraceButton->setMinimumHeight(50);
        fixedTraceButton->setToolTip("Open fixed position display mode - each message type is assigned to a specific line");
        connect(fixedTraceButton, &QPushButton::clicked, this, &MainWindow::openFixedPositionTraceWindow);
        
        layout->addWidget(traceButton);
        layout->addWidget(fixedTraceButton);
        layout->addStretch();
    }
    
    QString ascFilePath;
    QString dbcFilePath;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    if (argc < 3)
    {
        QMessageBox::critical(nullptr, "Error", "Usage: CanLogViewer <asc_log_path> <dbc_path>");
        return -1;
    }
    
    QString ascPath = argv[1];
    QString dbcPath = argv[2];
    
    MainWindow mainWindow(ascPath, dbcPath);
    mainWindow.show();
    
    return app.exec();
}

#include "main.moc"
