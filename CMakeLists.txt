cmake_minimum_required(VERSION 3.16)
project(CanLogViewer LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(Qt6 COMPONENTS Core Gui Widgets REQUIRED)

qt_standard_project_setup()

qt_add_executable(CanLogViewer
    src/main.cpp
    src/CANLogModel.cpp
    src/CANLogModel.h
    src/Parser.cpp
    src/Parser.h
    src/TraceWindow.cpp
    src/TraceWindow.h
    src/FixedPositionModel.cpp
    src/FixedPositionModel.h
    src/FixedPositionTraceWindow.cpp
    src/FixedPositionTraceWindow.h
    src/DBCParser.cpp
    src/DBCParser.h
)

target_include_directories(CanLogViewer PRIVATE src)
target_link_libraries(CanLogViewer PRIVATE Qt6::Core Qt6::Gui Qt6::Widgets)
