#pragma once
#include <QObject>
#include <QString>
#include <QMap>
#include <QVector>
#include <QRegularExpression>
#include <QLocale>
#include <QStringList>
#include <exception>

enum class SignalByteOrder {
    BigEndian,    // Motorola
    LittleEndian  // Intel
};

enum class SignalValueType {
    Unsigned,
    Signed
};

struct DBCSignal
{
    QString name;
    QString description;
    int startBit;
    int length;
    SignalByteOrder byteOrder;
    SignalValueType valueType;
    double factor;
    double offset;
    double minValue;
    double maxValue;
    QString unit;
    QMap<int, QString> valueTable; // For enumerated values
    QStringList receivers; // Signal receivers
    
    // Legacy compatibility
    QString getByteOrderString() const {
        return (byteOrder == SignalByteOrder::BigEndian) ? "big" : "little";
    }
    QString getValueTypeString() const {
        return (valueType == SignalValueType::Unsigned) ? "Unsigned" : "Signed";
    }
};

struct DBCMessage
{
    QString name;
    QString description;
    uint32_t id;
    int dlc;
    QString sender;
    QVector<DBCSignal> signalList;
    QMap<QString, QString> attributes; // Message attributes
    int cycleTime = 0; // Message cycle time in ms
};

class DBCParseException : public std::exception
{
public:
    explicit DBCParseException(const QString& message) : m_message(message.toStdString()) {}
    const char* what() const noexcept override { return m_message.c_str(); }
private:
    std::string m_message;
};

class DBCParser : public QObject
{
    Q_OBJECT
public:
    explicit DBCParser(QObject *parent = nullptr);
    
    // Main interface
    bool loadDBC(const QString &filePath);
    bool loadDBCFromString(const QString &content);
    void clear();
    bool isLoaded() const;
    QString getLastError() const;
    
    // Message and signal access
    QVector<DBCSignal> getSignalsForMessage(uint32_t messageId) const;
    QString getMessageName(uint32_t messageId) const;
    QVector<uint32_t> getAllMessageIds() const;
    QVector<DBCMessage> getAllMessages() const;
    bool hasMessage(uint32_t messageId) const;
    const DBCMessage* getMessage(uint32_t messageId) const;
    
    // Signal processing
    double extractSignalValue(const DBCSignal &signal, const QVector<uint8_t> &data) const;
    QString formatSignalValue(const DBCSignal &signal, double value) const;
    bool validateSignalValue(const DBCSignal &signal, double value) const;
    
    // DBC information
    QString getVersion() const;
    QStringList getNodes() const;
    QMap<QString, QMap<int, QString>> getValueTables() const;
    
    // Signal encoding/decoding
    QByteArray encodeSignal(const DBCSignal &signal, double value) const;
    
private:
    // Parsing methods
    bool validateDBCFormat(const QString &content);
    void parseVersion(const QString &content);
    void parseNewSymbols(const QString &content);
    void parseBitTiming(const QString &content);
    void parseNodes(const QString &content);
    void parseMessages(const QString &content);
    void parseValueTables(const QString &content);
    void parseSignalComments(const QString &content);
    void parseMessageComments(const QString &content);
    void parseAttributeDefinitions(const QString &content);
    void parseAttributeDefaults(const QString &content);
    void parseAttributeValues(const QString &content);
    void parseValueDescriptions(const QString &content);
    
    // Helper methods
    uint64_t extractBits(const QVector<uint8_t> &data, int startBit, int length, bool bigEndian) const;
    double parseDouble(const QString &str) const;
    int parseInt(const QString &str) const;
    QString sanitizeString(const QString &str) const;
    
    // Member variables
    QMap<uint32_t, DBCMessage> messages;
    QMap<QString, QMap<int, QString>> valueTables;
    QMap<QString, QString> attributeDefinitions;
    QMap<QString, QString> attributeDefaults;
    
    QString m_version;
    QStringList m_nodes;
    QStringList m_newSymbols;
    QString m_lastError;
    bool m_isLoaded;
    QLocale m_locale;
};