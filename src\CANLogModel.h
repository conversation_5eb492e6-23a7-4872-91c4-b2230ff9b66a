#pragma once
#include <QAbstractTableModel>
#include <QVector>

struct CANLog
{
    double timestamp;
    int channel;
    QString id;
    QString direction;
    int dlc;
    QVector<uint8_t> data;
    QString messageName; // From DBC
};

class CANLogModel : public QAbstractTableModel
{
    Q_OBJECT
public:
    CANLogModel(QObject *parent = nullptr);
    int rowCount(const QModelIndex &) const override;
    int columnCount(const QModelIndex &) const override;
    QVariant data(const QModelIndex &, int role) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role) const override;
    
    void setDeltaTimeEnabled(bool enabled);
    bool isDeltaTimeEnabled() const { return deltaTimeEnabled; }
    
    const CANLog& getLog(int index) const;
    void clear();

public slots:
    void appendLogs(const QVector<CANLog> &batch);

private:
    QVector<CANLog> logs;
    bool deltaTimeEnabled;
    double baseTimestamp;
};
