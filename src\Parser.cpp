#include "Parser.h"
#include <QFile>
#include <QTextStream>
#include <QRegularExpression>
#include <QFileInfo>
#include <QDebug>

Parser::Parser(const QString &file, CANLogModel *m, DBCParser *dbc)
    : filename(file), model(m), dbcParser(dbc)
{
    if (model)
    {
        connect(this, &Parser::logsParsed, model, &CANLogModel::appendLogs);
    }
}

void Parser::run()
{
    QFile f(filename);
    if (!f.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        qWarning() << "Failed to open ASC file:" << filename;
        emit parsingFinished();
        return;
    }
    
    // Get file size for progress calculation
    QFileInfo fileInfo(f);
    qint64 fileSize = fileInfo.size();
    qint64 bytesRead = 0;
    
    QTextStream in(&f);
    QVector<CANLog> batch;
    batch.reserve(10000);
    
    // Enhanced regex to handle different ASC formats
    static QRegularExpression rx(R"(^([\d\.]+)\s+(\d+)\s+([A-Fa-f0-9]+)x?\s+(\w+)\s+d\s+(\d+)\s+(.*)$)");
    
    int lineCount = 0;
    int lastProgress = -1;
    
    while (!in.atEnd())
    {
        QString line = in.readLine();
        bytesRead += line.length() + 1; // +1 for newline
        lineCount++;
        
        // Update progress every 1000 lines
        if (lineCount % 1000 == 0)
        {
            int progress = static_cast<int>((bytesRead * 100) / fileSize);
            if (progress != lastProgress)
            {
                emit progressUpdated(progress);
                lastProgress = progress;
            }
        }
        
        auto m = rx.match(line);
        if (m.hasMatch())
        {
            CANLog log;
            log.timestamp = m.captured(1).toDouble();
            log.channel = m.captured(2).toInt();
            log.id = m.captured(3);
            log.direction = m.captured(4);
            log.dlc = m.captured(5).toInt();
            
            // Parse data bytes
            QString dataStr = m.captured(6).trimmed();
            if (!dataStr.isEmpty())
            {
                QStringList bytes = dataStr.split(' ', Qt::SkipEmptyParts);
                for (const QString &b : bytes)
                {
                    bool ok;
                    uint8_t byte = static_cast<uint8_t>(b.toUInt(&ok, 16));
                    if (ok)
                        log.data.append(byte);
                }
            }
            
            // Get message name from DBC if available
            if (dbcParser)
            {
                bool ok;
                uint32_t messageId = log.id.toUInt(&ok, 16);
                if (ok)
                {
                    log.messageName = dbcParser->getMessageName(messageId);
                }
            }
            
            batch.append(log);
            
            // Emit batch when it reaches threshold
            if (batch.size() >= 5000)
            {
                emit logsParsed(batch);
                emit logBatchReady(batch);
                batch.clear();
            }
        }
    }
    
    // Emit remaining logs
    if (!batch.isEmpty())
    {
        emit logsParsed(batch);
        emit logBatchReady(batch);
    }
    
    emit progressUpdated(100);
    emit parsingFinished();
    
    // qDebug() << "Parsing completed. Total lines processed:" << lineCount;
}
