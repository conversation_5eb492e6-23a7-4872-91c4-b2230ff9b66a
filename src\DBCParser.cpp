#include "DBCParser.h"
#include <QFile>
#include <QTextStream>
#include <QRegularExpression>
#include <QDebug>
#include <QtEndian>
#include <QStringList>
#include <QLocale>

DBCParser::DBCParser(QObject *parent)
    : QObject(parent), m_isLoaded(false)
{
    // Initialize locale for consistent float parsing
    m_locale = QLocale::c();
}

bool DBCParser::loadDBC(const QString &filePath)
{
    // Clear previous data
    clear();
    
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        m_lastError = QString("Failed to open DBC file: %1").arg(filePath);
        qWarning() << m_lastError;
        return false;
    }
    
    QTextStream in(&file);
    QString content = in.readAll();
    
    // Validate DBC file format
    if (!validateDBCFormat(content))
    {
        m_lastError = "Invalid DBC file format";
        return false;
    }
    
    try {
        parseVersion(content);
        parseNewSymbols(content);
        parseBitTiming(content);
        parseNodes(content);
        parseValueTables(content);
        parseMessages(content);
        parseSignalComments(content);
        parseMessageComments(content);
        parseAttributeDefinitions(content);
        parseAttributeDefaults(content);
        parseAttributeValues(content);
        parseValueDescriptions(content);
        
        m_isLoaded = true;
        qDebug() << "Successfully loaded" << messages.size() << "messages from DBC";
        return true;
    }
    catch (const std::exception& e) {
        m_lastError = QString("Error parsing DBC file: %1").arg(e.what());
        qWarning() << m_lastError;
        return false;
    }
}

// New helper methods implementation
void DBCParser::clear()
{
    messages.clear();
    valueTables.clear();
    attributeDefinitions.clear();
    attributeDefaults.clear();
    m_version.clear();
    m_nodes.clear();
    m_newSymbols.clear();
    m_lastError.clear();
    m_isLoaded = false;
}

bool DBCParser::loadDBCFromString(const QString &content)
{
    clear();
    
    if (!validateDBCFormat(content))
    {
        m_lastError = "Invalid DBC file format";
        return false;
    }
    
    try {
        parseVersion(content);
        parseNewSymbols(content);
        parseBitTiming(content);
        parseNodes(content);
        parseValueTables(content);
        parseMessages(content);
        parseSignalComments(content);
        parseMessageComments(content);
        parseAttributeDefinitions(content);
        parseAttributeDefaults(content);
        parseAttributeValues(content);
        parseValueDescriptions(content);
        
        m_isLoaded = true;
        qDebug() << "Successfully loaded" << messages.size() << "messages from DBC string";
        return true;
    }
    catch (const std::exception& e) {
        m_lastError = QString("Error parsing DBC content: %1").arg(e.what());
        qWarning() << m_lastError;
        return false;
    }
}

bool DBCParser::validateDBCFormat(const QString &content)
{
    // Basic validation - check for essential DBC keywords
    if (!content.contains("VERSION") && !content.contains("BO_"))
    {
        return false;
    }
    return true;
}

void DBCParser::parseVersion(const QString &content)
{
    QRegularExpression versionRegex(R"(VERSION\s+\"([^\"]+)\")");
    QRegularExpressionMatch match = versionRegex.match(content);
    if (match.hasMatch())
    {
        m_version = match.captured(1);
    }
}

void DBCParser::parseNewSymbols(const QString &content)
{
    QRegularExpression newSymbolsRegex(R"(NEW_SYMBOLS_\s*:\s*([^\n]+))");
    QRegularExpressionMatch match = newSymbolsRegex.match(content);
    if (match.hasMatch())
    {
        QString symbols = match.captured(1).trimmed();
        m_newSymbols = symbols.split(QRegularExpression("\\s+"), Qt::SkipEmptyParts);
    }
}

void DBCParser::parseBitTiming(const QString &content)
{
    // Parse bit timing information if needed
    // BS_: [baudrate] : [BTR1],[BTR2]
    QRegularExpression bitTimingRegex(R"(BS_\s*:\s*(\d+)\s*:\s*(\d+)\s*,\s*(\d+))");
    QRegularExpressionMatch match = bitTimingRegex.match(content);
    if (match.hasMatch())
    {
        // Store bit timing info if needed
        // qDebug() << "Bit timing found:" << match.captured(0);
    }
}

void DBCParser::parseNodes(const QString &content)
{
    QRegularExpression nodesRegex(R"(BU_\s*:\s*([^\n]+))");
    QRegularExpressionMatch match = nodesRegex.match(content);
    if (match.hasMatch())
    {
        QString nodes = match.captured(1).trimmed();
        m_nodes = nodes.split(QRegularExpression("\\s+"), Qt::SkipEmptyParts);
    }
}

void DBCParser::parseValueTables(const QString &content)
{
    QRegularExpression valueTableRegex(R"(VAL_TABLE_\s+(\w+)\s+([^;]+);)");
    QRegularExpressionMatchIterator it = valueTableRegex.globalMatch(content);
    
    while (it.hasNext())
    {
        QRegularExpressionMatch match = it.next();
        QString tableName = match.captured(1);
        QString values = match.captured(2);
        
        QMap<int, QString> valueMap;
        QRegularExpression valueRegex(R"((\d+)\s+\"([^\"]+)\")");
        QRegularExpressionMatchIterator valueIt = valueRegex.globalMatch(values);
        
        while (valueIt.hasNext())
        {
            QRegularExpressionMatch valueMatch = valueIt.next();
            int key = parseInt(valueMatch.captured(1));
            QString value = sanitizeString(valueMatch.captured(2));
            valueMap[key] = value;
        }
        
        valueTables[tableName] = valueMap;
    }
}

void DBCParser::parseMessages(const QString &content)
{
    // Parse message definitions
    QRegularExpression messageRegex(R"(BO_\s+(\d+)\s+(\w+):\s*(\d+)\s+(\w+))");
    QRegularExpressionMatchIterator msgIt = messageRegex.globalMatch(content);
    
    while (msgIt.hasNext())
    {
        QRegularExpressionMatch msgMatch = msgIt.next();
        
        DBCMessage message;
        message.id = parseInt(msgMatch.captured(1));
        message.name = sanitizeString(msgMatch.captured(2));
        message.dlc = parseInt(msgMatch.captured(3));
        message.sender = sanitizeString(msgMatch.captured(4));
        
        // Find signals for this message
        int msgStart = msgMatch.capturedEnd();
        int msgEnd = content.indexOf(QRegularExpression(R"(^BO_|^$)"), msgStart);
        if (msgEnd == -1) msgEnd = content.length();
        
        QString messageBlock = content.mid(msgStart, msgEnd - msgStart);
        
        // Parse signals
        QRegularExpression signalRegex(R"SIG(SG_\s+(\w+)\s*:\s*(\d+)\|(\d+)@(\d+)([+-])\s*\(([^,]+),([^)]+)\)\s*\[([^|]+)\|([^]]+)\]\s*"([^"]*)")SIG");

        QRegularExpressionMatchIterator sigIt = signalRegex.globalMatch(messageBlock);
        
        while (sigIt.hasNext())
        {
            QRegularExpressionMatch sigMatch = sigIt.next();
            
            DBCSignal signal;
            signal.name = sanitizeString(sigMatch.captured(1));
            signal.startBit = parseInt(sigMatch.captured(2));
            signal.length = parseInt(sigMatch.captured(3));
            signal.byteOrder = (sigMatch.captured(4) == "0") ? SignalByteOrder::BigEndian : SignalByteOrder::LittleEndian;
            signal.valueType = (sigMatch.captured(5) == "-") ? SignalValueType::Signed : SignalValueType::Unsigned;
            signal.factor = parseDouble(sigMatch.captured(6));
            signal.offset = parseDouble(sigMatch.captured(7));
            signal.minValue = parseDouble(sigMatch.captured(8));
            signal.maxValue = parseDouble(sigMatch.captured(9));
            signal.unit = sanitizeString(sigMatch.captured(10));
            
            message.signalList.append(signal);
        }
        
        messages[message.id] = message;
    }
    
    // Parse VAL_ entries for signal value tables
    QRegularExpression valRegex(R"(VAL_\s+(\d+)\s+(\w+)\s+([^;]+);)");
    QRegularExpressionMatchIterator valIt = valRegex.globalMatch(content);
    
    while (valIt.hasNext())
    {
        QRegularExpressionMatch valMatch = valIt.next();
        uint32_t messageId = valMatch.captured(1).toUInt();
        QString signalName = valMatch.captured(2);
        QString values = valMatch.captured(3);
        
        if (messages.contains(messageId))
        {
            // Find the signal and add value table
            for (auto &signal : messages[messageId].signalList)
            {
                if (signal.name == signalName)
                {
                    QRegularExpression valueRegex(R"((\d+)\s+\"([^\"]+)\")");
                    QRegularExpressionMatchIterator valueIt = valueRegex.globalMatch(values);
                    
                    while (valueIt.hasNext())
                    {
                        QRegularExpressionMatch valueMatch = valueIt.next();
                        int key = valueMatch.captured(1).toInt();
                        QString value = valueMatch.captured(2);
                        signal.valueTable[key] = value;
                    }
                    break;
                }
            }
        }
    }
}

void DBCParser::parseSignalComments(const QString &content)
{
    QRegularExpression commentRegex(R"(CM_\s+SG_\s+(\d+)\s+(\w+)\s+\"([^\"]+)\";)");
    QRegularExpressionMatchIterator it = commentRegex.globalMatch(content);
    
    while (it.hasNext())
    {
        QRegularExpressionMatch match = it.next();
        uint32_t messageId = match.captured(1).toUInt();
        QString signalName = match.captured(2);
        QString description = match.captured(3);
        
        if (messages.contains(messageId))
        {
            for (auto &signal : messages[messageId].signalList)
            {
                if (signal.name == signalName)
                {
                    signal.description = description;
                    break;
                }
            }
        }
    }
}

QVector<DBCSignal> DBCParser::getSignalsForMessage(uint32_t messageId) const
{
    if (messages.contains(messageId))
    {
        return messages[messageId].signalList;
    }
    return QVector<DBCSignal>();
}

QString DBCParser::getMessageName(uint32_t messageId) const
{
    if (messages.contains(messageId))
    {
        return messages[messageId].name;
    }
    return QString("Unknown_0x%1").arg(messageId, 0, 16);
}

uint64_t DBCParser::extractBits(const QVector<uint8_t> &data, int startBit, int length, bool bigEndian) const
{
    if (data.isEmpty() || startBit < 0 || length <= 0 || startBit + length > data.size() * 8)
        return 0;
    
    uint64_t result = 0;
    
    if (bigEndian)
    {
        // Big endian (Motorola format)
        for (int i = 0; i < length; i++)
        {
            int bitPos = startBit - i;
            int byteIndex = bitPos / 8;
            int bitIndex = bitPos % 8;
            
            if (byteIndex >= 0 && byteIndex < data.size())
            {
                uint8_t byte = data[byteIndex];
                uint8_t bit = (byte >> bitIndex) & 1;
                result |= (static_cast<uint64_t>(bit) << (length - 1 - i));
            }
        }
    }
    else
    {
        // Little endian (Intel format)
        for (int i = 0; i < length; i++)
        {
            int bitPos = startBit + i;
            int byteIndex = bitPos / 8;
            int bitIndex = bitPos % 8;
            
            if (byteIndex < data.size())
            {
                uint8_t byte = data[byteIndex];
                uint8_t bit = (byte >> bitIndex) & 1;
                result |= (static_cast<uint64_t>(bit) << i);
            }
        }
    }
    
    return result;
}

double DBCParser::extractSignalValue(const DBCSignal &signal, const QVector<uint8_t> &data) const
{
    bool bigEndian = (signal.byteOrder == SignalByteOrder::BigEndian);
    uint64_t rawValue = extractBits(data, signal.startBit, signal.length, bigEndian);
    
    double value;
    if (signal.valueType == SignalValueType::Signed)
    {
        // Handle signed values
        int64_t signedValue = static_cast<int64_t>(rawValue);
        if (rawValue & (1ULL << (signal.length - 1)))
        {
            // Negative number - sign extend
            signedValue |= (~0ULL << signal.length);
        }
        value = static_cast<double>(signedValue);
    }
    else
    {
        value = static_cast<double>(rawValue);
    }
    
    return value * signal.factor + signal.offset;
}

// Additional parsing methods
void DBCParser::parseMessageComments(const QString &content)
{
    QRegularExpression commentRegex(R"(CM_\s+BO_\s+(\d+)\s+\"([^\"]+)\";)");
    QRegularExpressionMatchIterator it = commentRegex.globalMatch(content);
    
    while (it.hasNext())
    {
        QRegularExpressionMatch match = it.next();
        uint32_t messageId = parseInt(match.captured(1));
        QString description = sanitizeString(match.captured(2));
        
        if (messages.contains(messageId))
        {
            messages[messageId].description = description;
        }
    }
}

void DBCParser::parseAttributeDefinitions(const QString &content)
{
    QRegularExpression attrDefRegex(R"(BA_DEF_\s+(\w+)\s+\"([^\"]+)\"\s+([^;]+);)");
    QRegularExpressionMatchIterator it = attrDefRegex.globalMatch(content);
    
    while (it.hasNext())
    {
        QRegularExpressionMatch match = it.next();
        QString objectType = sanitizeString(match.captured(1));
        QString attributeName = sanitizeString(match.captured(2));
        QString attributeType = sanitizeString(match.captured(3));
        
        attributeDefinitions[attributeName] = attributeType;
    }
}

void DBCParser::parseAttributeDefaults(const QString &content)
{
    QRegularExpression attrDefaultRegex(R"(BA_DEF_DEF_\s+\"([^\"]+)\"\s+([^;]+);)");
    QRegularExpressionMatchIterator it = attrDefaultRegex.globalMatch(content);
    
    while (it.hasNext())
    {
        QRegularExpressionMatch match = it.next();
        QString attributeName = sanitizeString(match.captured(1));
        QString defaultValue = sanitizeString(match.captured(2));
        
        attributeDefaults[attributeName] = defaultValue;
    }
}

void DBCParser::parseAttributeValues(const QString &content)
{
    QRegularExpression attrValueRegex(R"(BA_\s+\"([^\"]+)\"\s+([^;]+);)");
    QRegularExpressionMatchIterator it = attrValueRegex.globalMatch(content);
    
    while (it.hasNext())
    {
        QRegularExpressionMatch match = it.next();
        QString attributeName = sanitizeString(match.captured(1));
        QString attributeValue = sanitizeString(match.captured(2));
        
        // Parse attribute values for messages, signals, etc.
        // This is a simplified implementation
        // qDebug() << "Attribute:" << attributeName << "Value:" << attributeValue;
    }
}

void DBCParser::parseValueDescriptions(const QString &content)
{
    QRegularExpression valDescRegex(R"(VAL_\s+(\d+)\s+(\w+)\s+([^;]+);)");
    QRegularExpressionMatchIterator it = valDescRegex.globalMatch(content);
    
    while (it.hasNext())
    {
        QRegularExpressionMatch match = it.next();
        uint32_t messageId = parseInt(match.captured(1));
        QString signalName = sanitizeString(match.captured(2));
        QString values = match.captured(3);
        
        if (messages.contains(messageId))
        {
            for (auto &signal : messages[messageId].signalList)
            {
                if (signal.name == signalName)
                {
                    QRegularExpression valueRegex(R"((\d+)\s+\"([^\"]+)\")");
                    QRegularExpressionMatchIterator valueIt = valueRegex.globalMatch(values);
                    
                    while (valueIt.hasNext())
                    {
                        QRegularExpressionMatch valueMatch = valueIt.next();
                        int key = parseInt(valueMatch.captured(1));
                        QString value = sanitizeString(valueMatch.captured(2));
                        signal.valueTable[key] = value;
                    }
                    break;
                }
            }
        }
    }
}

// Helper methods
int DBCParser::parseInt(const QString &str) const
{
    bool ok;
    int value = str.toInt(&ok, 0); // Auto-detect base (hex, decimal)
    if (!ok)
    {
        qWarning() << "Failed to parse integer:" << str;
        return 0;
    }
    return value;
}

double DBCParser::parseDouble(const QString &str) const
{
    bool ok;
    double value = m_locale.toDouble(str, &ok);
    if (!ok)
    {
        // Try with standard locale
        value = str.toDouble(&ok);
        if (!ok)
        {
            qWarning() << "Failed to parse double:" << str;
            return 0.0;
        }
    }
    return value;
}

QString DBCParser::sanitizeString(const QString &str) const
{
    QString result = str.trimmed();
    // Remove quotes if present
    if (result.startsWith('"') && result.endsWith('"'))
    {
        result = result.mid(1, result.length() - 2);
    }
    return result;
}

// New public methods
QVector<uint32_t> DBCParser::getAllMessageIds() const
{
    QVector<uint32_t> ids;
    for (auto it = messages.begin(); it != messages.end(); ++it)
    {
        ids.append(it.key());
    }
    return ids;
}

QVector<DBCMessage> DBCParser::getAllMessages() const
{
    QVector<DBCMessage> messageList;
    for (auto it = messages.begin(); it != messages.end(); ++it)
    {
        messageList.append(it.value());
    }
    return messageList;
}

bool DBCParser::hasMessage(uint32_t messageId) const
{
    return messages.contains(messageId);
}

const DBCMessage* DBCParser::getMessage(uint32_t messageId) const
{
    auto it = messages.find(messageId);
    if (it != messages.end())
    {
        return &it.value();
    }
    return nullptr;
}

bool DBCParser::validateSignalValue(const DBCSignal &signal, double value) const
{
    return (value >= signal.minValue && value <= signal.maxValue);
}

QByteArray DBCParser::encodeSignal(const DBCSignal &signal, double value) const
{
    // Convert physical value to raw value
    double rawValue = (value - signal.offset) / signal.factor;
    
    // Convert to integer based on signal type
    uint64_t intValue;
    if (signal.valueType == SignalValueType::Signed)
    {
        int64_t signedValue = static_cast<int64_t>(rawValue);
        intValue = static_cast<uint64_t>(signedValue);
    }
    else
    {
        intValue = static_cast<uint64_t>(rawValue);
    }
    
    // Mask to signal length
    uint64_t mask = (1ULL << signal.length) - 1;
    intValue &= mask;
    
    // Create byte array (simplified implementation)
    QByteArray result(8, 0); // Assume max 8 bytes
    
    if (signal.byteOrder == SignalByteOrder::LittleEndian)
    {
        // Little endian encoding
        for (int i = 0; i < 8; i++)
        {
            result[i] = static_cast<char>((intValue >> (i * 8)) & 0xFF);
        }
    }
    else
    {
        // Big endian encoding
        for (int i = 0; i < 8; i++)
        {
            result[7 - i] = static_cast<char>((intValue >> (i * 8)) & 0xFF);
        }
    }
    
    return result;
}

QString DBCParser::formatSignalValue(const DBCSignal &signal, double value) const
{
    // Check if there's a value table entry
    int intValue = static_cast<int>(value);
    if (signal.valueTable.contains(intValue))
    {
        return QString("%1 (%2)").arg(signal.valueTable[intValue]).arg(value);
    }
    
    // Format based on unit and precision
    if (signal.unit.isEmpty())
    {
        return QString::number(value, 'g', 6);
    }
    else
    {
        return QString("%1 %2").arg(value, 0, 'g', 6).arg(signal.unit);
    }
}

bool DBCParser::isLoaded() const
{
    return m_isLoaded;
}

QString DBCParser::getLastError() const
{
    return m_lastError;
}

QString DBCParser::getVersion() const
{
    return m_version;
}

QStringList DBCParser::getNodes() const
{
    return m_nodes;
}

QMap<QString, QMap<int, QString>> DBCParser::getValueTables() const
{
    return valueTables;
}