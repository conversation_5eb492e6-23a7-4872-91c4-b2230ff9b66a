#include "FixedPositionModel.h"
#include <QColor>
#include <QFont>
#include <QDebug>
#include <QTimer>
#include <algorithm>
#include <QCoreApplication>

FixedPositionModel::FixedPositionModel(QObject *parent)
    : QAbstractTableModel(parent), deltaTimeEnabled(false), baseTimestamp(0.0)
{
    // Reserve space for better performance
    entries.reserve(MAX_ENTRIES);
    messageKeyToRow.reserve(MAX_ENTRIES);
    
    // Timer-based processing removed - using immediate processing instead
    processingTimer = nullptr;
}

FixedPositionModel::~FixedPositionModel()
{
    // No timer to clean up - using immediate processing
}

int FixedPositionModel::rowCount(const QModelIndex &) const
{
    return entries.size();
}

int FixedPositionModel::columnCount(const QModelIndex &) const
{
    return 7; // Timestamp, Channel, ID, Message Name, Direction, DLC, Data
}

QVariant FixedPositionModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() >= entries.size())
        return QVariant();
    
    const FixedPositionEntry &entry = entries[index.row()];
    
    if (role == Qt::DisplayRole)
    {
        switch (index.column())
        {
        case 0: // Timestamp
            if (deltaTimeEnabled && baseTimestamp > 0.0)
                return QString::number(entry.lastTimestamp - baseTimestamp, 'f', 6);
            else
                return QString::number(entry.lastTimestamp, 'f', 6);
        case 1: // Channel
            return entry.channel;
        case 2: // ID
            return entry.messageId;
        case 3: // Message Name
            return entry.messageName.isEmpty() ? "Unknown" : entry.messageName;
        case 4: // Direction
            return entry.direction;
        case 5: // DLC
            return entry.dlc;
        case 6: // Data
        {
            // Optimized data string creation
            QString dataStr;
            dataStr.reserve(entry.lastData.size() * 3);
            for (int i = 0; i < entry.lastData.size(); ++i)
            {
                if (i > 0) dataStr += ' ';
                dataStr += QString::number(entry.lastData[i], 16).rightJustified(2, '0').toUpper();
            }
            return dataStr;
        }
        }
    }
    
    return QVariant();
}

QVariant FixedPositionModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal && role == Qt::DisplayRole)
    {
        switch (section)
        {
        case 0: return deltaTimeEnabled ? "Delta Time" : "Timestamp";
        case 1: return "Channel";
        case 2: return "ID";
        case 3: return "Message Name";
        case 4: return "Direction";
        case 5: return "DLC";
        case 6: return "Data";
        }
    }
    return QVariant();
}

void FixedPositionModel::setDeltaTimeEnabled(bool enabled)
{
    if (deltaTimeEnabled != enabled)
    {
        deltaTimeEnabled = enabled;
        emit headerDataChanged(Qt::Horizontal, 0, 0);
        emit dataChanged(index(0, 0), index(rowCount(QModelIndex()) - 1, 0));
    }
}

const FixedPositionEntry& FixedPositionModel::getEntry(int index) const
{
    return entries[index];
}

void FixedPositionModel::clear()
{
    beginResetModel();
    entries.clear();
    messageKeyToRow.clear();
    baseTimestamp = 0.0;
    // No timer to stop - using immediate processing
    endResetModel();
}

int FixedPositionModel::getRowForMessageKey(const MessageKey &messageKey) const
{
    return messageKeyToRow.value(messageKey, -1);
}

MessageKey FixedPositionModel::createMessageKey(int channel, const QString &messageId) const
{
    return qMakePair(channel, messageId);
}

void FixedPositionModel::appendLogs(const QVector<CANLog> &batch)
{
    // Process logs immediately in small chunks to avoid UI blocking
    // This eliminates the timer-based approach that was causing continuous updates

    const int IMMEDIATE_CHUNK_SIZE = 100; // Process larger chunks immediately

    for (int i = 0; i < batch.size(); i += IMMEDIATE_CHUNK_SIZE) {
        int chunkEnd = qMin(i + IMMEDIATE_CHUNK_SIZE, batch.size());
        QVector<CANLog> chunk = batch.mid(i, chunkEnd - i);

        // Process chunk immediately
        processChunk(chunk);

        // Allow UI to update between chunks for large batches
        if (batch.size() > IMMEDIATE_CHUNK_SIZE && (i + IMMEDIATE_CHUNK_SIZE) < batch.size()) {
            QCoreApplication::processEvents();
        }
    }
}

// Timer-based processing removed - now using immediate processing in appendLogs()

void FixedPositionModel::processChunk(const QVector<CANLog> &chunk)
{
    if (chunk.isEmpty()) {
        return;
    }
    
    // Check if we need cleanup
    if (entries.size() >= CLEANUP_THRESHOLD) {
        enforceMaxEntries();
    }
    
    QVector<CANLog> newLogs;
    QSet<int> updatedRows;
    
    // Set base timestamp from first log if not set
    if (baseTimestamp == 0.0 && !chunk.isEmpty()) {
        baseTimestamp = chunk.first().timestamp;
    }
    
    // Process logs efficiently
    for (const CANLog &log : chunk) {
        MessageKey messageKey = createMessageKey(log.channel, log.id);
        int row = getRowForMessageKey(messageKey);
        
        if (row == -1) {
            // New message type - collect for batch insertion
            newLogs.append(log);
        } else {
            // Update existing entry - just overwrite with latest data
            FixedPositionEntry &entry = entries[row];
            entry.lastTimestamp = log.timestamp;
            entry.channel = log.channel;
            entry.direction = log.direction;
            entry.dlc = log.dlc;
            entry.lastData = log.data;
            updatedRows.insert(row);
        }
    }
    
    // Batch insert new entries
    if (!newLogs.isEmpty()) {
        int startRow = entries.size();
        beginInsertRows(QModelIndex(), startRow, startRow + newLogs.size() - 1);
        
        for (const CANLog &log : newLogs) {
            FixedPositionEntry entry;
            entry.messageId = log.id;
            entry.messageName = log.messageName;
            entry.lastTimestamp = log.timestamp;
            entry.channel = log.channel;
            entry.direction = log.direction;
            entry.dlc = log.dlc;
            entry.lastData = log.data;
            
            entries.append(entry);
            MessageKey messageKey = createMessageKey(log.channel, log.id);
            messageKeyToRow[messageKey] = entries.size() - 1;
        }
        
        endInsertRows();
    }
    
    // Emit efficient dataChanged signals for updated rows
    if (!updatedRows.isEmpty()) {
        // Convert to sorted list for range optimization
        QList<int> sortedRows = updatedRows.values();
        std::sort(sortedRows.begin(), sortedRows.end());
        
        // Emit signals for consecutive ranges
        int rangeStart = sortedRows.first();
        int rangeEnd = rangeStart;
        
        for (int i = 1; i < sortedRows.size(); ++i) {
            if (sortedRows[i] == rangeEnd + 1) {
                rangeEnd = sortedRows[i];
            } else {
                emit dataChanged(index(rangeStart, 0), index(rangeEnd, columnCount(QModelIndex()) - 1));
                rangeStart = rangeEnd = sortedRows[i];
            }
        }
        
        // Emit final range
        emit dataChanged(index(rangeStart, 0), index(rangeEnd, columnCount(QModelIndex()) - 1));
    }
}

void FixedPositionModel::enforceMaxEntries()
{
    if (entries.size() <= MAX_ENTRIES) {
        return;
    }

    int entriesToRemove = entries.size() - MAX_ENTRIES;

    beginRemoveRows(QModelIndex(), 0, entriesToRemove - 1);

    // Remove entries
    entries.remove(0, entriesToRemove);

    // Rebuild key mappings
    messageKeyToRow.clear();
    for (int i = 0; i < entries.size(); ++i) {
        const FixedPositionEntry &entry = entries[i];
        MessageKey key = createMessageKey(entry.channel, entry.messageId);
        messageKeyToRow[key] = i;
    }

    endRemoveRows();

    qDebug() << "FixedPositionModel: Cleaned up" << entriesToRemove << "entries, now have" << entries.size();
}
