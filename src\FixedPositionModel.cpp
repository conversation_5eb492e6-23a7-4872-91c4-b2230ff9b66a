#include "FixedPositionModel.h"
#include <QColor>
#include <QFont>
#include <QDebug>
#include <QMutexLocker>
#include <QApplication>
#include <algorithm>

FixedPositionModel::FixedPositionModel(QObject *parent)
    : QAbstractTableModel(parent), deltaTimeEnabled(false), baseTimestamp(0.0),
      workerThread(nullptr), worker(nullptr), isShuttingDown(false)
{
    // Reserve space for better performance
    entries.reserve(MAX_ENTRIES);
    messageKeyToRow.reserve(MAX_ENTRIES);
    
    // Initialize UI update timer
    uiUpdateTimer = new QTimer(this);
    uiUpdateTimer->setSingleShot(false);
    uiUpdateTimer->setInterval(UI_UPDATE_INTERVAL);
    connect(uiUpdateTimer, &QTimer::timeout, this, &FixedPositionModel::onUIUpdateTimer);
    
    // Initialize worker thread
    initializeWorker();
}

FixedPositionModel::~FixedPositionModel()
{
    shutdownWorker();
}

void FixedPositionModel::initializeWorker()
{
    // Create worker thread
    workerThread = new QThread(this);
    worker = new FixedPositionWorker();
    worker->moveToThread(workerThread);
    
    // Connect signals with queued connections for thread safety
    connect(this, &FixedPositionModel::processLogs, worker, &FixedPositionWorker::processLogChunk, Qt::QueuedConnection);
    connect(worker, &FixedPositionWorker::resultReady, this, &FixedPositionModel::onProcessedResult, Qt::QueuedConnection);
    connect(workerThread, &QThread::finished, worker, &QObject::deleteLater);
    
    // Start worker thread
    workerThread->start();
    
    qDebug() << "FixedPositionModel: Async worker initialized";
}

void FixedPositionModel::shutdownWorker()
{
    if (isShuttingDown.testAndSetOrdered(0, 1)) {
        qDebug() << "FixedPositionModel: Shutting down async worker";
        
        if (uiUpdateTimer) {
            uiUpdateTimer->stop();
        }
        
        if (workerThread && workerThread->isRunning()) {
            workerThread->quit();
            if (!workerThread->wait(3000)) {
                qWarning() << "Worker thread did not finish gracefully, terminating";
                workerThread->terminate();
                workerThread->wait(1000);
            }
        }
        
        worker = nullptr; // Will be deleted by thread finished signal
        workerThread = nullptr;
    }
}

int FixedPositionModel::rowCount(const QModelIndex &) const
{
    QMutexLocker locker(&dataMutex);
    return entries.size();
}

int FixedPositionModel::columnCount(const QModelIndex &) const
{
    return 7; // Timestamp, Channel, ID, Message Name, Direction, DLC, Data
}

QVariant FixedPositionModel::data(const QModelIndex &index, int role) const
{
    QMutexLocker locker(&dataMutex);
    
    if (!index.isValid() || index.row() >= entries.size())
        return QVariant();
    
    const FixedPositionEntry &entry = entries[index.row()];
    
    if (role == Qt::DisplayRole)
    {
        switch (index.column())
        {
        case 0: // Timestamp
            if (deltaTimeEnabled && baseTimestamp > 0.0)
                return QString::number(entry.lastTimestamp - baseTimestamp, 'f', 6);
            else
                return QString::number(entry.lastTimestamp, 'f', 6);
        case 1: // Channel
            return entry.channel;
        case 2: // ID
            return entry.messageId;
        case 3: // Message Name
            return entry.messageName.isEmpty() ? "Unknown" : entry.messageName;
        case 4: // Direction
            return entry.direction;
        case 5: // DLC
            return entry.dlc;
        case 6: // Data
        {
            // Optimized data string creation
            QString dataStr;
            dataStr.reserve(entry.lastData.size() * 3);
            for (int i = 0; i < entry.lastData.size(); ++i)
            {
                if (i > 0) dataStr += ' ';
                dataStr += QString::number(entry.lastData[i], 16).rightJustified(2, '0').toUpper();
            }
            return dataStr;
        }
        }
    }
    
    return QVariant();
}

QVariant FixedPositionModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal && role == Qt::DisplayRole)
    {
        switch (section)
        {
        case 0: return deltaTimeEnabled ? "Delta Time" : "Timestamp";
        case 1: return "Channel";
        case 2: return "ID";
        case 3: return "Message Name";
        case 4: return "Direction";
        case 5: return "DLC";
        case 6: return "Data";
        }
    }
    return QVariant();
}

void FixedPositionModel::setDeltaTimeEnabled(bool enabled)
{
    if (deltaTimeEnabled != enabled)
    {
        deltaTimeEnabled = enabled;
        emit headerDataChanged(Qt::Horizontal, 0, 0);
        emit dataChanged(index(0, 0), index(rowCount(QModelIndex()) - 1, 0));
    }
}

const FixedPositionEntry& FixedPositionModel::getEntry(int index) const
{
    QMutexLocker locker(&dataMutex);
    return entries[index];
}

void FixedPositionModel::clear()
{
    QMutexLocker locker(&dataMutex);
    beginResetModel();
    entries.clear();
    messageKeyToRow.clear();
    baseTimestamp = 0.0;
    endResetModel();
}

int FixedPositionModel::getRowForMessageKey(const MessageKey &messageKey) const
{
    QMutexLocker locker(&dataMutex);
    return messageKeyToRow.value(messageKey, -1);
}

MessageKey FixedPositionModel::createMessageKey(int channel, const QString &messageId) const
{
    return qMakePair(channel, messageId);
}

void FixedPositionModel::appendLogs(const QVector<CANLog> &batch)
{
    if (isShuttingDown.loadAcquire() || !worker) {
        return; // Don't process during shutdown
    }
    
    // Break large batches into small chunks for responsive processing
    for (int i = 0; i < batch.size(); i += CHUNK_SIZE) {
        int chunkEnd = qMin(i + CHUNK_SIZE, batch.size());
        QVector<CANLog> chunk = batch.mid(i, chunkEnd - i);
        
        // Send chunk to worker thread asynchronously
        emit processLogs(chunk);
    }
}

void FixedPositionModel::onProcessedResult(const ProcessedResult &result)
{
    if (isShuttingDown.loadAcquire()) {
        return; // Don't process results during shutdown
    }

    // Queue result for UI thread processing
    {
        QMutexLocker locker(&resultsMutex);
        pendingResults.enqueue(result);
    }

    // Start UI update timer if not running
    if (!uiUpdateTimer->isActive()) {
        uiUpdateTimer->start();
    }
}

void FixedPositionModel::onUIUpdateTimer()
{
    ProcessedResult result;
    bool hasResult = false;

    // Get one result from queue
    {
        QMutexLocker locker(&resultsMutex);
        if (!pendingResults.isEmpty()) {
            result = pendingResults.dequeue();
            hasResult = true;
        }
    }

    if (hasResult) {
        applyProcessedResult(result);

        // Update worker state
        if (worker) {
            QMutexLocker locker(&dataMutex);
            worker->setCurrentState(entries, messageKeyToRow, baseTimestamp);
        }
    }

    // Stop timer if no more results
    {
        QMutexLocker locker(&resultsMutex);
        if (pendingResults.isEmpty()) {
            uiUpdateTimer->stop();
        }
    }
}

void FixedPositionModel::applyProcessedResult(const ProcessedResult &result)
{
    QMutexLocker locker(&dataMutex);

    // Check if we need cleanup
    if (entries.size() >= CLEANUP_THRESHOLD) {
        enforceMaxEntries();
    }

    // Update base timestamp
    if (baseTimestamp == 0.0 && result.baseTimestamp > 0.0) {
        baseTimestamp = result.baseTimestamp;
    }

    // Add new entries
    if (!result.newEntries.isEmpty()) {
        int startRow = entries.size();
        beginInsertRows(QModelIndex(), startRow, startRow + result.newEntries.size() - 1);

        entries.append(result.newEntries);

        // Update key mappings
        for (auto it = result.newKeyMappings.constBegin(); it != result.newKeyMappings.constEnd(); ++it) {
            messageKeyToRow[it.key()] = it.value() + startRow;
        }

        endInsertRows();
    }

    // Update existing entries
    if (!result.updatedEntries.isEmpty()) {
        QSet<int> updatedRows;

        for (auto it = result.updatedEntries.constBegin(); it != result.updatedEntries.constEnd(); ++it) {
            int row = it.key();
            if (row >= 0 && row < entries.size()) {
                entries[row] = it.value();
                updatedRows.insert(row);
            }
        }

        // Emit dataChanged for updated rows in ranges
        if (!updatedRows.isEmpty()) {
            QList<int> sortedRows = updatedRows.values();
            std::sort(sortedRows.begin(), sortedRows.end());

            int rangeStart = sortedRows.first();
            int rangeEnd = rangeStart;

            for (int i = 1; i < sortedRows.size(); ++i) {
                if (sortedRows[i] == rangeEnd + 1) {
                    rangeEnd = sortedRows[i];
                } else {
                    emit dataChanged(index(rangeStart, 0), index(rangeEnd, columnCount(QModelIndex()) - 1));
                    rangeStart = rangeEnd = sortedRows[i];
                }
            }

            emit dataChanged(index(rangeStart, 0), index(rangeEnd, columnCount(QModelIndex()) - 1));
        }
    }
}

void FixedPositionModel::enforceMaxEntries()
{
    if (entries.size() <= MAX_ENTRIES) {
        return;
    }

    int entriesToRemove = entries.size() - MAX_ENTRIES;

    beginRemoveRows(QModelIndex(), 0, entriesToRemove - 1);

    // Remove entries
    entries.remove(0, entriesToRemove);

    // Rebuild key mappings
    messageKeyToRow.clear();
    for (int i = 0; i < entries.size(); ++i) {
        const FixedPositionEntry &entry = entries[i];
        MessageKey key = createMessageKey(entry.channel, entry.messageId);
        messageKeyToRow[key] = i;
    }

    endRemoveRows();

    qDebug() << "FixedPositionModel: Cleaned up" << entriesToRemove << "entries, now have" << entries.size();
}

// FixedPositionWorker Implementation
FixedPositionWorker::FixedPositionWorker(QObject *parent)
    : QObject(parent), currentBaseTimestamp(0.0)
{
}

void FixedPositionWorker::setCurrentState(const QVector<FixedPositionEntry> &entries,
                                         const QHash<MessageKey, int> &keyToRow,
                                         double baseTimestamp)
{
    QMutexLocker locker(&stateMutex);
    currentEntries = entries;
    currentKeyToRow = keyToRow;
    currentBaseTimestamp = baseTimestamp;
}

void FixedPositionWorker::processLogChunk(const QVector<CANLog> &logs)
{
    if (logs.isEmpty()) {
        return;
    }

    ProcessedResult result;

    // Get current state
    QVector<FixedPositionEntry> workingEntries;
    QHash<MessageKey, int> workingKeyToRow;
    double workingBaseTimestamp;

    {
        QMutexLocker locker(&stateMutex);
        workingEntries = currentEntries;
        workingKeyToRow = currentKeyToRow;
        workingBaseTimestamp = currentBaseTimestamp;
    }

    result.baseTimestamp = workingBaseTimestamp;

    // Process logs efficiently
    for (const CANLog &log : logs) {
        if (result.baseTimestamp == 0.0) {
            result.baseTimestamp = log.timestamp;
        }

        MessageKey messageKey = createMessageKey(log.channel, log.id);
        int row = workingKeyToRow.value(messageKey, -1);

        if (row == -1) {
            // New entry
            FixedPositionEntry entry;
            entry.messageId = log.id;
            entry.messageName = log.messageName;
            entry.lastTimestamp = log.timestamp;
            entry.channel = log.channel;
            entry.direction = log.direction;
            entry.dlc = log.dlc;
            entry.lastData = log.data;

            int newIndex = result.newEntries.size();
            result.newEntries.append(entry);
            result.newKeyMappings[messageKey] = newIndex;
            workingKeyToRow[messageKey] = workingEntries.size() + newIndex;
        } else {
            // Update existing entry
            FixedPositionEntry updatedEntry;
            if (row < workingEntries.size()) {
                updatedEntry = workingEntries[row];
            }
            updatedEntry.lastTimestamp = log.timestamp;
            updatedEntry.channel = log.channel;
            updatedEntry.direction = log.direction;
            updatedEntry.dlc = log.dlc;
            updatedEntry.lastData = log.data;

            result.updatedEntries[row] = updatedEntry;
            if (row < workingEntries.size()) {
                workingEntries[row] = updatedEntry;
            }
        }
    }

    // Update our working state
    {
        QMutexLocker locker(&stateMutex);
        currentEntries = workingEntries;
        currentEntries.append(result.newEntries);

        for (auto it = result.newKeyMappings.constBegin(); it != result.newKeyMappings.constEnd(); ++it) {
            currentKeyToRow[it.key()] = currentEntries.size() - result.newEntries.size() + it.value();
        }

        if (currentBaseTimestamp == 0.0 && result.baseTimestamp > 0.0) {
            currentBaseTimestamp = result.baseTimestamp;
        }
    }

    // Emit result
    emit resultReady(result);
}

MessageKey FixedPositionWorker::createMessageKey(int channel, const QString &messageId) const
{
    return qMakePair(channel, messageId);
}
