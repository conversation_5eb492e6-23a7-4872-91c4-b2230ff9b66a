#include "FixedPositionModel.h"
#include <QColor>
#include <QFont>
#include <QSet>
#include <QMutexLocker>
#include <QDebug>
#include <QMetaObject>
#include <algorithm>

FixedPositionModel::FixedPositionModel(QObject *parent)
    : QAbstractTableModel(parent), deltaTimeEnabled(false), baseTimestamp(0.0),
      batchProcessing(false), workerThread(nullptr), worker(nullptr), isShuttingDown(false)
{
    pendingLogs.reserve(BATCH_SIZE);

    // Initialize UI update timer (disabled for now)
    uiUpdateTimer = new QTimer(this);
    uiUpdateTimer->setSingleShot(false);
    uiUpdateTimer->setInterval(UI_UPDATE_INTERVAL_MS);
    connect(uiUpdateTimer, &QTimer::timeout, this, &FixedPositionModel::onUIUpdateTimer);

    // TODO: Re-enable worker thread after fixing MOC compilation
    // initializeWorkerThread();
}

int FixedPositionModel::rowCount(const QModelIndex &) const
{
    return entries.size();
}

int FixedPositionModel::columnCount(const QModelIndex &) const
{
    return 8; // Timestamp, Channel, ID, Message Name, Direction, DLC, Data, Count
}

QVariant FixedPositionModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() >= entries.size())
        return QVariant();

    const FixedPositionEntry &entry = entries[index.row()];
    
    if (role == Qt::DisplayRole)
    {
        switch (index.column())
        {
        case 0: // Timestamp
            if (deltaTimeEnabled && baseTimestamp > 0.0)
                return QString::number(entry.lastTimestamp - baseTimestamp, 'f', 6);
            else
                return QString::number(entry.lastTimestamp, 'f', 6);
        case 1: // Channel
            return entry.channel;
        case 2: // ID
            return entry.messageId;
        case 3: // Message Name
            return entry.messageName.isEmpty() ? "Unknown" : entry.messageName;
        case 4: // Direction
            return entry.direction;
        case 5: // DLC
            return entry.dlc;
        case 6: // Data
        {
            QString dataStr;
            for (int i = 0; i < entry.lastData.size(); ++i)
            {
                if (i > 0) dataStr += " ";
                dataStr += QString("%1").arg(entry.lastData[i], 2, 16, QChar('0')).toUpper();
            }
            return dataStr;
        }
        case 7: // Update Count
            return entry.updateCount;
        }
    }
    else if (role == Qt::BackgroundRole)
    {
        // Highlight recently updated entries
        if (entry.updateCount > 1)
        {
            return QColor(255, 255, 200); // Light yellow for updated entries
        }
    }
    else if (role == Qt::FontRole)
    {
        if (entry.updateCount > 1)
        {
            QFont font;
            font.setBold(true);
            return font;
        }
    }
    
    return QVariant();
}

QVariant FixedPositionModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal && role == Qt::DisplayRole)
    {
        switch (section)
        {
        case 0: return deltaTimeEnabled ? "Delta Time" : "Timestamp";
        case 1: return "Channel";
        case 2: return "ID";
        case 3: return "Message Name";
        case 4: return "Direction";
        case 5: return "DLC";
        case 6: return "Data";
        case 7: return "Count";
        }
    }
    return QVariant();
}

void FixedPositionModel::setDeltaTimeEnabled(bool enabled)
{
    if (deltaTimeEnabled != enabled)
    {
        deltaTimeEnabled = enabled;
        emit headerDataChanged(Qt::Horizontal, 0, 0);
        emit dataChanged(index(0, 0), index(rowCount(QModelIndex()) - 1, 0));
    }
}

const FixedPositionEntry& FixedPositionModel::getEntry(int index) const
{
    return entries[index];
}

void FixedPositionModel::clear()
{
    beginResetModel();
    entries.clear();
    messageKeyToRow.clear();
    baseTimestamp = 0.0;
    endResetModel();
}

int FixedPositionModel::getRowForMessageKey(const QString &messageKey) const
{
    return messageKeyToRow.value(messageKey, -1);
}

QString FixedPositionModel::createMessageKey(int channel, const QString &messageId) const
{
    return QString("%1:%2").arg(channel).arg(messageId);
}

void FixedPositionModel::appendLogs(const QVector<CANLog> &batch)
{
    batchProcessing = true;
    pendingLogs.reserve(pendingLogs.size() + batch.size());

    for (const CANLog &log : batch)
    {
        pendingLogs.append(log);

        if (pendingLogs.size() >= BATCH_SIZE)
        {
            processBatch();
        }
    }

    // Process remaining logs
    if (!pendingLogs.isEmpty())
    {
        processBatch();
    }

    batchProcessing = false;
}

void FixedPositionModel::processBatch()
{
    if (pendingLogs.isEmpty())
        return;

    // For now, fall back to synchronous processing to avoid UI hanging
    // TODO: Implement proper async processing after fixing MOC issues
    QVector<CANLog> newEntries;
    updatedRows.clear();

    // Process all pending logs
    for (const CANLog &log : pendingLogs)
    {
        if (baseTimestamp == 0.0)
        {
            baseTimestamp = log.timestamp;
        }

        QString messageKey = createMessageKey(log.channel, log.id);
        int row = getRowForMessageKey(messageKey);

        if (row == -1)
        {
            // Collect new entries to add in batch
            newEntries.append(log);
        }
        else
        {
            // Update existing entry without emitting signals
            FixedPositionEntry &entry = entries[row];
            entry.lastTimestamp = log.timestamp;
            entry.channel = log.channel;
            entry.direction = log.direction;
            entry.dlc = log.dlc;
            entry.lastData = log.data;
            entry.updateCount++;

            updatedRows.insert(row);
        }
    }

    // Add new entries in batch
    if (!newEntries.isEmpty())
    {
        beginInsertRows(QModelIndex(), entries.size(), entries.size() + newEntries.size() - 1);

        for (const CANLog &log : newEntries)
        {
            FixedPositionEntry entry;
            entry.messageId = log.id;
            entry.messageName = log.messageName;
            entry.lastTimestamp = log.timestamp;
            entry.channel = log.channel;
            entry.direction = log.direction;
            entry.dlc = log.dlc;
            entry.lastData = log.data;
            entry.updateCount = 1;

            entries.append(entry);
            QString messageKey = createMessageKey(log.channel, log.id);
            messageKeyToRow[messageKey] = entries.size() - 1;
        }

        endInsertRows();
    }

    // Emit dataChanged for updated rows in batch
    if (!updatedRows.isEmpty())
    {
        // Group consecutive rows for efficient updates
        QList<int> sortedRows = updatedRows.values();
        std::sort(sortedRows.begin(), sortedRows.end());

        int start = sortedRows.first();
        int end = start;

        for (int i = 1; i < sortedRows.size(); ++i)
        {
            if (sortedRows[i] == end + 1)
            {
                end = sortedRows[i];
            }
            else
            {
                // Emit for current range
                emit dataChanged(index(start, 0), index(end, columnCount(QModelIndex()) - 1));
                start = end = sortedRows[i];
            }
        }

        // Emit for last range
        emit dataChanged(index(start, 0), index(end, columnCount(QModelIndex()) - 1));
    }

    pendingLogs.clear();
}

FixedPositionModel::~FixedPositionModel()
{
    shutdownWorkerThread();
}

void FixedPositionModel::initializeWorkerThread()
{
    // Create worker thread
    workerThread = new QThread(this);
    worker = new FixedPositionWorker();
    worker->moveToThread(workerThread);

    // Connect signals with queued connections for thread safety
    connect(this, &FixedPositionModel::destroyed, worker, &FixedPositionWorker::deleteLater);
    connect(worker, &FixedPositionWorker::batchProcessed,
            this, &FixedPositionModel::onBatchProcessed, Qt::QueuedConnection);

    // Start the worker thread
    workerThread->start();

    qDebug() << "FixedPositionModel: Worker thread initialized";
}

void FixedPositionModel::shutdownWorkerThread()
{
    if (isShuttingDown.testAndSetOrdered(0, 1)) {
        qDebug() << "FixedPositionModel: Shutting down worker thread";

        if (uiUpdateTimer) {
            uiUpdateTimer->stop();
        }

        if (workerThread && workerThread->isRunning()) {
            workerThread->quit();
            if (!workerThread->wait(3000)) { // Wait up to 3 seconds
                qWarning() << "FixedPositionModel: Worker thread did not finish gracefully, terminating";
                workerThread->terminate();
                workerThread->wait(1000);
            }
        }

        if (worker) {
            worker->deleteLater();
            worker = nullptr;
        }

        if (workerThread) {
            workerThread->deleteLater();
            workerThread = nullptr;
        }
    }
}

void FixedPositionModel::onBatchProcessed(const ProcessedBatch &result)
{
    if (isShuttingDown.loadAcquire()) {
        return; // Don't process results during shutdown
    }

    try {
        // Add to pending UI updates
        {
            QMutexLocker locker(&uiUpdateMutex);
            pendingUIUpdates.append(result);
        }

        // Start UI update timer if not already running
        if (!uiUpdateTimer->isActive()) {
            uiUpdateTimer->start();
        }
    } catch (const std::exception &e) {
        qWarning() << "FixedPositionModel::onBatchProcessed exception:" << e.what();
    } catch (...) {
        qWarning() << "FixedPositionModel::onBatchProcessed unknown exception";
    }
}

void FixedPositionModel::onUIUpdateTimer()
{
    QVector<ProcessedBatch> updates;

    // Get limited number of pending updates to prevent UI blocking
    {
        QMutexLocker locker(&uiUpdateMutex);
        if (pendingUIUpdates.isEmpty()) {
            uiUpdateTimer->stop();
            return;
        }

        int updateCount = qMin(pendingUIUpdates.size(), MAX_UI_UPDATES_PER_TICK);
        updates.reserve(updateCount);

        for (int i = 0; i < updateCount; ++i) {
            updates.append(pendingUIUpdates.takeFirst());
        }
    }

    // Apply updates in batch
    for (const ProcessedBatch &update : updates) {
        applyProcessedBatch(update);
    }

    // Continue timer if more updates are pending
    {
        QMutexLocker locker(&uiUpdateMutex);
        if (pendingUIUpdates.isEmpty()) {
            uiUpdateTimer->stop();
        }
        // Timer continues running if there are more updates
    }
}

void FixedPositionModel::applyProcessedBatch(const ProcessedBatch &result)
{
    QMutexLocker locker(&dataMutex);

    // Update base timestamp if needed
    if (baseTimestamp == 0.0 && result.baseTimestamp > 0.0) {
        baseTimestamp = result.baseTimestamp;
    }

    // Add new entries
    if (!result.newEntries.isEmpty()) {
        beginInsertRows(QModelIndex(), entries.size(), entries.size() + result.newEntries.size() - 1);

        entries.append(result.newEntries);

        // Update message key mappings
        for (auto it = result.newMessageKeyMappings.constBegin();
             it != result.newMessageKeyMappings.constEnd(); ++it) {
            messageKeyToRow[it.key()] = it.value();
        }

        endInsertRows();
    }

    // Update existing entries
    if (!result.updatedEntries.isEmpty()) {
        QSet<int> updatedRowSet;

        for (const auto &update : result.updatedEntries) {
            int row = update.first;
            const FixedPositionEntry &entry = update.second;

            if (row >= 0 && row < entries.size()) {
                entries[row] = entry;
                updatedRowSet.insert(row);
            }
        }

        // Emit dataChanged for updated rows in batches
        if (!updatedRowSet.isEmpty()) {
            QList<int> sortedRows = updatedRowSet.values();
            std::sort(sortedRows.begin(), sortedRows.end());

            int start = sortedRows.first();
            int end = start;

            for (int i = 1; i < sortedRows.size(); ++i) {
                if (sortedRows[i] == end + 1) {
                    end = sortedRows[i];
                } else {
                    emit dataChanged(index(start, 0), index(end, columnCount(QModelIndex()) - 1));
                    start = end = sortedRows[i];
                }
            }

            // Emit for last range
            emit dataChanged(index(start, 0), index(end, columnCount(QModelIndex()) - 1));
        }
    }
}

void FixedPositionModel::addNewEntry(const CANLog &log)
{
    beginInsertRows(QModelIndex(), entries.size(), entries.size());
    
    FixedPositionEntry entry;
    entry.messageId = log.id;
    entry.messageName = log.messageName;
    entry.lastTimestamp = log.timestamp;
    entry.channel = log.channel;
    entry.direction = log.direction;
    entry.dlc = log.dlc;
    entry.lastData = log.data;
    entry.updateCount = 1;
    
    entries.append(entry);
    QString messageKey = createMessageKey(log.channel, log.id);
    messageKeyToRow[messageKey] = entries.size() - 1;
    
    endInsertRows();
}

void FixedPositionModel::updateExistingEntry(int row, const CANLog &log)
{
    FixedPositionEntry &entry = entries[row];
    entry.lastTimestamp = log.timestamp;
    entry.channel = log.channel;
    entry.direction = log.direction;
    entry.dlc = log.dlc;
    entry.lastData = log.data;
    entry.updateCount++;
    
    // Update the entire row
    emit dataChanged(index(row, 0), index(row, columnCount(QModelIndex()) - 1));
}

// FixedPositionWorker implementation
FixedPositionWorker::FixedPositionWorker(QObject *parent)
    : QObject(parent), currentBaseTimestamp(0.0)
{
}

void FixedPositionWorker::setCurrentState(const QVector<FixedPositionEntry> &entries,
                                         const QMap<QString, int> &messageKeyToRow,
                                         double baseTimestamp)
{
    QMutexLocker locker(&stateMutex);
    currentEntries = entries;
    currentMessageKeyToRow = messageKeyToRow;
    currentBaseTimestamp = baseTimestamp;
}

void FixedPositionWorker::processBatch(const QVector<CANLog> &logs)
{
    if (logs.isEmpty()) {
        return;
    }

    try {
        ProcessedBatch result;

    // Get current state
    QVector<FixedPositionEntry> workingEntries;
    QMap<QString, int> workingMessageKeyToRow;
    double workingBaseTimestamp;

    {
        QMutexLocker locker(&stateMutex);
        workingEntries = currentEntries;
        workingMessageKeyToRow = currentMessageKeyToRow;
        workingBaseTimestamp = currentBaseTimestamp;
    }

    result.baseTimestamp = workingBaseTimestamp;

    // Process all logs
    for (const CANLog &log : logs) {
        // Set base timestamp if not set
        if (result.baseTimestamp == 0.0) {
            result.baseTimestamp = log.timestamp;
        }

        QString messageKey = createMessageKey(log.channel, log.id);
        int row = workingMessageKeyToRow.value(messageKey, -1);

        if (row == -1) {
            // New entry
            FixedPositionEntry entry;
            entry.messageId = log.id;
            entry.messageName = log.messageName;
            entry.lastTimestamp = log.timestamp;
            entry.channel = log.channel;
            entry.direction = log.direction;
            entry.dlc = log.dlc;
            entry.lastData = log.data;
            entry.updateCount = 1;

            result.newEntries.append(entry);

            // Calculate new row index
            int newRowIndex = workingEntries.size() + result.newEntries.size() - 1;
            result.newMessageKeyMappings[messageKey] = newRowIndex;
            workingMessageKeyToRow[messageKey] = newRowIndex;
        } else {
            // Update existing entry
            if (row < workingEntries.size()) {
                FixedPositionEntry updatedEntry = workingEntries[row];
                updatedEntry.lastTimestamp = log.timestamp;
                updatedEntry.channel = log.channel;
                updatedEntry.direction = log.direction;
                updatedEntry.dlc = log.dlc;
                updatedEntry.lastData = log.data;
                updatedEntry.updateCount++;

                result.updatedEntries.append(qMakePair(row, updatedEntry));
                workingEntries[row] = updatedEntry; // Update working copy
            }
        }
    }

    // Update our state for next batch
    {
        QMutexLocker locker(&stateMutex);
        currentEntries = workingEntries;
        currentEntries.append(result.newEntries);

        for (auto it = result.newMessageKeyMappings.constBegin();
             it != result.newMessageKeyMappings.constEnd(); ++it) {
            currentMessageKeyToRow[it.key()] = it.value();
        }

        if (currentBaseTimestamp == 0.0 && result.baseTimestamp > 0.0) {
            currentBaseTimestamp = result.baseTimestamp;
        }
    }

        // Emit the result
        emit batchProcessed(result);

    } catch (const std::exception &e) {
        qWarning() << "FixedPositionWorker::processBatch exception:" << e.what();
        // Don't emit result on error - this will cause the batch to be lost
        // but prevents corrupting the model
    } catch (...) {
        qWarning() << "FixedPositionWorker::processBatch unknown exception";
        // Don't emit result on error
    }
}

QString FixedPositionWorker::createMessageKey(int channel, const QString &messageId) const
{
    return QString("%1:%2").arg(channel).arg(messageId);
}