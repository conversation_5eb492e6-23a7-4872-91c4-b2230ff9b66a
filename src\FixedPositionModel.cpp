#include "FixedPositionModel.h"
#include <QColor>
#include <QFont>
#include <QDebug>
#include <algorithm>

FixedPositionModel::FixedPositionModel(QObject *parent)
    : QAbstractTableModel(parent), deltaTimeEnabled(false), baseTimestamp(0.0), batchProcessing(false)
{
    // Reserve space for better performance
    pendingLogs.reserve(BATCH_SIZE);
    entries.reserve(MAX_ENTRIES);
    messageKeyToRow.reserve(MAX_ENTRIES);
}

FixedPositionModel::~FixedPositionModel()
{
    // Simple cleanup - no threading components to manage
}

int FixedPositionModel::rowCount(const QModelIndex &) const
{
    return entries.size();
}

int FixedPositionModel::columnCount(const QModelIndex &) const
{
    return 7; // Timestamp, Channel, ID, Message Name, Direction, DLC, Data
}

QVariant FixedPositionModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() >= entries.size())
        return QVariant();
    
    const FixedPositionEntry &entry = entries[index.row()];
    
    if (role == Qt::DisplayRole)
    {
        switch (index.column())
        {
        case 0: // Timestamp
            if (deltaTimeEnabled && baseTimestamp > 0.0)
                return QString::number(entry.lastTimestamp - baseTimestamp, 'f', 6);
            else
                return QString::number(entry.lastTimestamp, 'f', 6);
        case 1: // Channel
            return entry.channel;
        case 2: // ID
            return entry.messageId;
        case 3: // Message Name
            return entry.messageName.isEmpty() ? "Unknown" : entry.messageName;
        case 4: // Direction
            return entry.direction;
        case 5: // DLC
            return entry.dlc;
        case 6: // Data
        {
            // Optimized data string creation
            QString dataStr;
            dataStr.reserve(entry.lastData.size() * 3); // Pre-allocate space
            for (int i = 0; i < entry.lastData.size(); ++i)
            {
                if (i > 0) dataStr += ' ';
                dataStr += QString::number(entry.lastData[i], 16).rightJustified(2, '0').toUpper();
            }
            return dataStr;
        }
        }
    }
    
    return QVariant();
}

QVariant FixedPositionModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal && role == Qt::DisplayRole)
    {
        switch (section)
        {
        case 0: return deltaTimeEnabled ? "Delta Time" : "Timestamp";
        case 1: return "Channel";
        case 2: return "ID";
        case 3: return "Message Name";
        case 4: return "Direction";
        case 5: return "DLC";
        case 6: return "Data";
        }
    }
    return QVariant();
}

void FixedPositionModel::setDeltaTimeEnabled(bool enabled)
{
    if (deltaTimeEnabled != enabled)
    {
        deltaTimeEnabled = enabled;
        emit headerDataChanged(Qt::Horizontal, 0, 0);
        emit dataChanged(index(0, 0), index(rowCount(QModelIndex()) - 1, 0));
    }
}

const FixedPositionEntry& FixedPositionModel::getEntry(int index) const
{
    return entries[index];
}

void FixedPositionModel::clear()
{
    beginResetModel();
    entries.clear();
    messageKeyToRow.clear();
    baseTimestamp = 0.0;
    endResetModel();
}

int FixedPositionModel::getRowForMessageKey(const MessageKey &messageKey) const
{
    return messageKeyToRow.value(messageKey, -1);
}

MessageKey FixedPositionModel::createMessageKey(int channel, const QString &messageId) const
{
    return qMakePair(channel, messageId);
}

void FixedPositionModel::appendLogs(const QVector<CANLog> &batch)
{
    batchProcessing = true;
    pendingLogs.reserve(pendingLogs.size() + batch.size());
    
    for (const CANLog &log : batch)
    {
        pendingLogs.append(log);
        
        if (pendingLogs.size() >= BATCH_SIZE)
        {
            processBatch();
        }
    }
    
    // Process remaining logs
    if (!pendingLogs.isEmpty())
    {
        processBatch();
    }
    
    batchProcessing = false;
}

void FixedPositionModel::processBatch()
{
    if (pendingLogs.isEmpty())
        return;

    // Check if we need to enforce max entries limit
    if (entries.size() >= CLEANUP_THRESHOLD) {
        enforceMaxEntries();
    }

    QVector<CANLog> newLogs;
    updatedRows.clear();

    // Set base timestamp from first log if not set
    if (baseTimestamp == 0.0 && !pendingLogs.isEmpty()) {
        baseTimestamp = pendingLogs.first().timestamp;
    }

    // Process all pending logs efficiently
    for (const CANLog &log : pendingLogs) {
        MessageKey messageKey = createMessageKey(log.channel, log.id);
        int row = getRowForMessageKey(messageKey);

        if (row == -1) {
            // New message type - collect for batch insertion
            newLogs.append(log);
        } else {
            // Update existing entry - just overwrite with latest data
            FixedPositionEntry &entry = entries[row];
            entry.lastTimestamp = log.timestamp;
            entry.channel = log.channel;
            entry.direction = log.direction;
            entry.dlc = log.dlc;
            entry.lastData = log.data;
            updatedRows.insert(row);
        }
    }

    // Batch insert new entries
    if (!newLogs.isEmpty()) {
        int startRow = entries.size();
        beginInsertRows(QModelIndex(), startRow, startRow + newLogs.size() - 1);

        for (const CANLog &log : newLogs) {
            FixedPositionEntry entry;
            entry.messageId = log.id;
            entry.messageName = log.messageName;
            entry.lastTimestamp = log.timestamp;
            entry.channel = log.channel;
            entry.direction = log.direction;
            entry.dlc = log.dlc;
            entry.lastData = log.data;

            entries.append(entry);
            MessageKey messageKey = createMessageKey(log.channel, log.id);
            messageKeyToRow[messageKey] = entries.size() - 1;
        }

        endInsertRows();
    }

    // Emit efficient dataChanged signals for updated rows
    if (!updatedRows.isEmpty()) {
        // Convert to sorted list for range optimization
        QList<int> sortedRows = updatedRows.values();
        std::sort(sortedRows.begin(), sortedRows.end());

        // Emit signals for consecutive ranges
        int rangeStart = sortedRows.first();
        int rangeEnd = rangeStart;

        for (int i = 1; i < sortedRows.size(); ++i) {
            if (sortedRows[i] == rangeEnd + 1) {
                rangeEnd = sortedRows[i];
            } else {
                emit dataChanged(index(rangeStart, 0), index(rangeEnd, columnCount() - 1));
                rangeStart = rangeEnd = sortedRows[i];
            }
        }

        // Emit final range
        emit dataChanged(index(rangeStart, 0), index(rangeEnd, columnCount() - 1));
    }

    pendingLogs.clear();
}

void FixedPositionModel::enforceMaxEntries()
{
    if (entries.size() <= MAX_ENTRIES) {
        return; // No cleanup needed
    }

    int entriesToRemove = entries.size() - MAX_ENTRIES;

    // Remove oldest entries (from the beginning)
    beginRemoveRows(QModelIndex(), 0, entriesToRemove - 1);

    // Remove entries and update hash map
    for (int i = 0; i < entriesToRemove; ++i) {
        const FixedPositionEntry &entry = entries[i];
        MessageKey key = createMessageKey(entry.channel, entry.messageId);
        messageKeyToRow.remove(key);
    }

    entries.remove(0, entriesToRemove);

    // Update all remaining row indices in the hash map
    messageKeyToRow.clear();
    for (int i = 0; i < entries.size(); ++i) {
        const FixedPositionEntry &entry = entries[i];
        MessageKey key = createMessageKey(entry.channel, entry.messageId);
        messageKeyToRow[key] = i;
    }

    endRemoveRows();

    qDebug() << "FixedPositionModel: Cleaned up" << entriesToRemove << "entries, now have" << entries.size();
}

void FixedPositionModel::addNewEntry(const CANLog &log)
{
    beginInsertRows(QModelIndex(), entries.size(), entries.size());

    FixedPositionEntry entry;
    entry.messageId = log.id;
    entry.messageName = log.messageName;
    entry.lastTimestamp = log.timestamp;
    entry.channel = log.channel;
    entry.direction = log.direction;
    entry.dlc = log.dlc;
    entry.lastData = log.data;

    entries.append(entry);
    MessageKey messageKey = createMessageKey(log.channel, log.id);
    messageKeyToRow[messageKey] = entries.size() - 1;

    endInsertRows();
}

void FixedPositionModel::updateExistingEntry(int row, const CANLog &log)
{
    FixedPositionEntry &entry = entries[row];
    entry.lastTimestamp = log.timestamp;
    entry.channel = log.channel;
    entry.direction = log.direction;
    entry.dlc = log.dlc;
    entry.lastData = log.data;

    // Update the entire row
    emit dataChanged(index(row, 0), index(row, columnCount() - 1));
}
