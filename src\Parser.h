#pragma once
#include <QThread>
#include "CANLogModel.h"
#include "DBCParser.h"

class Parser : public QThread
{
    Q_OBJECT
public:
    Parser(const QString &file, CANLogModel *m, DBCParser *dbc = nullptr);

protected:
    void run() override;
    
signals:
    void logsParsed(QVector<CANLog>);
    void logBatchReady(const QVector<CANLog> &batch);
    void progressUpdated(int percentage);
    void parsingFinished();

private:
    QString filename;
    CANLogModel *model;
    DBCParser *dbcParser;
};
