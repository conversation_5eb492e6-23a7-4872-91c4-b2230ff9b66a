#include "CANLogModel.h"
#include <QtAlgorithms>

CANLogModel::CANLogModel(QObject *parent)
    : QAbstractTableModel(parent), deltaTimeEnabled(false), baseTimestamp(0.0) {}

int CANLogModel::rowCount(const QModelIndex &) const { return logs.size(); }
int CANLogModel::columnCount(const QModelIndex &) const { return 7; }

QVariant CANLogModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal && role == Qt::DisplayRole)
    {
        switch (section)
        {
        case 0: return deltaTimeEnabled ? "Delta Time" : "Timestamp";
        case 1: return "Channel";
        case 2: return "ID";
        case 3: return "Message Name";
        case 4: return "Direction";
        case 5: return "DLC";
        case 6: return "Data";
        }
    }
    return QAbstractTableModel::headerData(section, orientation, role);
}

QVariant CANLogModel::data(const QModelIndex &idx, int role) const
{
    if (role != Qt::DisplayRole)
        return {};
    
    const auto &l = logs.at(idx.row());
    switch (idx.column())
    {
    case 0:
        if (deltaTimeEnabled && !logs.isEmpty())
        {
            double deltaTime = l.timestamp - baseTimestamp;
            return QString::number(deltaTime, 'f', 6);
        }
        return QString::number(l.timestamp, 'f', 6);
    case 1:
        return l.channel;
    case 2:
        return l.id;
    case 3:
        return l.messageName.isEmpty() ? QString("Unknown") : l.messageName;
    case 4:
        return l.direction;
    case 5:
        return l.dlc;
    case 6:
    {
        QString s;
        for (auto b : l.data)
            s += QString("%1 ").arg(b, 2, 16, QLatin1Char('0'));
        return s.trimmed();
    }
    }
    return {};
}

void CANLogModel::setDeltaTimeEnabled(bool enabled)
{
    if (deltaTimeEnabled != enabled)
    {
        deltaTimeEnabled = enabled;
        if (enabled && !logs.isEmpty())
        {
            baseTimestamp = logs.first().timestamp;
        }
        emit headerDataChanged(Qt::Horizontal, 0, 0);
        emit dataChanged(index(0, 0), index(rowCount(QModelIndex()) - 1, 0));
    }
}

const CANLog& CANLogModel::getLog(int index) const
{
    return logs.at(index);
}

void CANLogModel::clear()
{
    beginResetModel();
    logs.clear();
    baseTimestamp = 0.0;
    endResetModel();
}

void CANLogModel::appendLogs(const QVector<CANLog> &batch)
{
    if (batch.isEmpty())
        return;
        
    int begin = logs.size();
    int end = begin + batch.size() - 1;
    beginInsertRows({}, begin, end);
    logs += batch;
    
    // Set base timestamp for delta time calculation
    if (deltaTimeEnabled && logs.size() == batch.size())
    {
        baseTimestamp = logs.first().timestamp;
    }
    
    endInsertRows();
}
