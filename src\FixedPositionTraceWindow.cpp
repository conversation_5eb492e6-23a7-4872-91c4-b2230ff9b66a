#include "FixedPositionTraceWindow.h"
#include <QApplication>
#include <QFileDialog>
#include <QMessageBox>
#include <QTextStream>
#include <QHeaderView>
#include <QTreeWidgetItem>
#include <QStatusBar>
#include <QMenuBar>
#include <QToolBar>
#include <QAction>
#include <QDebug>

FixedPositionTraceWindow::FixedPositionTraceWindow(const QString &ascPath, const QString &dbcPath, QWidget *parent)
    : QMainWindow(parent), ascFilePath(ascPath), dbcFilePath(dbcPath), deltaTimeEnabled(false)
{
    // Create the model first
    fixedModel = new FixedPositionModel(this);
    
    setupUI();
    loadDBC();
    
    // Create a custom parser that will work with our fixed position model
    parser = new Parser(ascFilePath, nullptr, dbcParser); // Pass nullptr for CANLogModel
    
    // Connect parser signals
    connect(parser, &Parser::progressUpdated, this, &FixedPositionTraceWindow::onParsingProgress);
    connect(parser, &Parser::parsingFinished, this, &FixedPositionTraceWindow::onParsingFinished);
    
    // Connect parser to our fixed model instead
    connect(parser, &Parser::logBatchReady, fixedModel, &FixedPositionModel::appendLogs);
    
    statusLabel->setText("Loading ASC file...");
    progressBar->setVisible(true);
    parser->start();
}

void FixedPositionTraceWindow::setupUI()
{
    setWindowTitle("CAN Trace Window - Fixed Position Mode");
    resize(1200, 800);
    
    centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);
    
    mainLayout = new QVBoxLayout(centralWidget);
    
    setupToolbar();
    
    // Create main splitter
    mainSplitter = new QSplitter(Qt::Horizontal, this);
    mainLayout->addWidget(mainSplitter);
    
    setupTable();
    setupSignalView();
    
    // Status bar
    statusLabel = new QLabel("Ready", this);
    progressBar = new QProgressBar(this);
    progressBar->setVisible(false);
    
    statusBar()->addWidget(statusLabel);
    statusBar()->addPermanentWidget(progressBar);
}

void FixedPositionTraceWindow::setupToolbar()
{
    QToolBar *toolbar = addToolBar("Main");
    
    // Delta Time button
    deltaTimeBtn = new QPushButton("Delta Time", this);
    deltaTimeBtn->setCheckable(true);
    deltaTimeBtn->setToolTip("Toggle delta time display");
    connect(deltaTimeBtn, &QPushButton::toggled, this, &FixedPositionTraceWindow::onDeltaTimeToggled);
    toolbar->addWidget(deltaTimeBtn);
    
    toolbar->addSeparator();
    
    // Clear Display button (specific to fixed position mode)
    clearDisplayBtn = new QPushButton("Clear Display", this);
    clearDisplayBtn->setToolTip("Clear all message entries from display");
    connect(clearDisplayBtn, &QPushButton::clicked, this, &FixedPositionTraceWindow::onClearDisplay);
    toolbar->addWidget(clearDisplayBtn);
    
    toolbar->addSeparator();
    
    // CSV Generate button
    csvGenerateBtn = new QPushButton("Generate CSV", this);
    csvGenerateBtn->setToolTip("Export current view to CSV");
    connect(csvGenerateBtn, &QPushButton::clicked, this, &FixedPositionTraceWindow::onGenerateCSV);
    toolbar->addWidget(csvGenerateBtn);
    
    toolbar->addSeparator();
    
    // Clear All Filters button
    clearFiltersBtn = new QPushButton("Clear All Filters", this);
    clearFiltersBtn->setToolTip("Clear all applied filters");
    connect(clearFiltersBtn, &QPushButton::clicked, this, &FixedPositionTraceWindow::onClearAllFilters);
    toolbar->addWidget(clearFiltersBtn);
    
    toolbar->addSeparator();
    
    // Column Configuration button
    columnConfigBtn = new QPushButton("Column Config", this);
    columnConfigBtn->setToolTip("Configure visible columns (Coming soon)");
    connect(columnConfigBtn, &QPushButton::clicked, this, &FixedPositionTraceWindow::onColumnConfiguration);
    toolbar->addWidget(columnConfigBtn);
}

void FixedPositionTraceWindow::setupTable()
{
    // Create proxy model for sorting/filtering
    proxyModel = new QSortFilterProxyModel(this);
    proxyModel->setSourceModel(fixedModel);
    proxyModel->setFilterCaseSensitivity(Qt::CaseInsensitive);
    
    // Create table view
    logTableView = new QTableView(this);
    logTableView->setModel(proxyModel);
    logTableView->setSortingEnabled(true);
    logTableView->setSelectionBehavior(QAbstractItemView::SelectRows);
    logTableView->setAlternatingRowColors(true);
    
    // Configure headers
    QHeaderView *header = logTableView->horizontalHeader();
    header->setStretchLastSection(true);
    header->setSectionResizeMode(QHeaderView::Interactive);
    
    // Set column widths
    logTableView->setColumnWidth(0, 120); // Timestamp/Delta
    logTableView->setColumnWidth(1, 80);  // Channel
    logTableView->setColumnWidth(2, 100); // ID
    logTableView->setColumnWidth(3, 200); // Message Name
    logTableView->setColumnWidth(4, 80);  // Direction
    logTableView->setColumnWidth(5, 60);  // DLC
    logTableView->setColumnWidth(6, 300); // Data
    logTableView->setColumnWidth(7, 80);  // Count
    
    // Connect selection signal
    connect(logTableView->selectionModel(), &QItemSelectionModel::currentRowChanged,
            this, &FixedPositionTraceWindow::onLogClicked);
    
    mainSplitter->addWidget(logTableView);
    mainSplitter->setSizes({800, 400});
}

void FixedPositionTraceWindow::setupSignalView()
{
    QWidget *signalWidget = new QWidget(this);
    QVBoxLayout *signalLayout = new QVBoxLayout(signalWidget);
    
    signalLabel = new QLabel("Signal Details", this);
    signalLabel->setStyleSheet("font-weight: bold; padding: 5px;");
    signalLayout->addWidget(signalLabel);
    
    signalTreeWidget = new QTreeWidget(this);
    signalTreeWidget->setHeaderLabels({"Signal Name", "Value", "Unit", "Description"});
    signalTreeWidget->setAlternatingRowColors(true);
    signalLayout->addWidget(signalTreeWidget);
    
    mainSplitter->addWidget(signalWidget);
}

void FixedPositionTraceWindow::loadDBC()
{
    dbcParser = new DBCParser(this);
    if (!dbcParser->loadDBC(dbcFilePath))
    {
        QMessageBox::warning(this, "Warning", 
                           QString("Failed to load DBC file: %1\nSignal parsing will not be available.")
                           .arg(dbcFilePath));
    }
}

void FixedPositionTraceWindow::onDeltaTimeToggled(bool enabled)
{
    deltaTimeEnabled = enabled;
    fixedModel->setDeltaTimeEnabled(enabled);
    deltaTimeBtn->setText(enabled ? "Absolute Time" : "Delta Time");
}

void FixedPositionTraceWindow::onGenerateCSV()
{
    QString fileName = QFileDialog::getSaveFileName(this, 
                                                   "Export to CSV", 
                                                   "can_log_fixed_export.csv", 
                                                   "CSV Files (*.csv)");
    if (fileName.isEmpty())
        return;
    
    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text))
    {
        QMessageBox::critical(this, "Error", "Failed to create CSV file.");
        return;
    }
    
    QTextStream out(&file);
    
    // Write header
    QStringList headers;
    for (int col = 0; col < fixedModel->columnCount(QModelIndex()); ++col)
    {
        headers << fixedModel->headerData(col, Qt::Horizontal, Qt::DisplayRole).toString();
    }
    out << headers.join(",") << "\n";
    
    // Write data
    for (int row = 0; row < proxyModel->rowCount(); ++row)
    {
        QStringList rowData;
        for (int col = 0; col < proxyModel->columnCount(); ++col)
        {
            QModelIndex index = proxyModel->index(row, col);
            QString data = proxyModel->data(index, Qt::DisplayRole).toString();
            // Escape commas and quotes in CSV
            if (data.contains(',') || data.contains('"'))
            {
                data = "\"" + data.replace('"', "\"\"") + "\"";
            }
            rowData << data;
        }
        out << rowData.join(",") << "\n";
    }
    
    QMessageBox::information(this, "Export Complete", 
                           QString("Successfully exported %1 message types to %2")
                           .arg(proxyModel->rowCount()).arg(fileName));
}

void FixedPositionTraceWindow::onClearAllFilters()
{
    proxyModel->setFilterRegularExpression(QRegularExpression());
    proxyModel->setFilterKeyColumn(-1);
    statusLabel->setText(QString("Filters cleared. Showing %1 message types.").arg(proxyModel->rowCount()));
}

void FixedPositionTraceWindow::onColumnConfiguration()
{
    QMessageBox::information(this, "Column Configuration", 
                           "Column configuration feature is coming soon!");
}

void FixedPositionTraceWindow::onClearDisplay()
{
    fixedModel->clear();
    signalTreeWidget->clear();
    signalLabel->setText("Signal Details");
    statusLabel->setText("Display cleared. Ready for new data.");
}

void FixedPositionTraceWindow::onLogClicked(const QModelIndex &index)
{
    if (!index.isValid())
        return;
    
    // Map proxy index to source index
    QModelIndex sourceIndex = proxyModel->mapToSource(index);
    updateSignalView(sourceIndex.row());
}

void FixedPositionTraceWindow::updateSignalView(int entryIndex)
{
    signalTreeWidget->clear();
    
    if (entryIndex < 0 || entryIndex >= fixedModel->rowCount(QModelIndex()))
        return;
    
    const FixedPositionEntry &entry = fixedModel->getEntry(entryIndex);
    
    // Update label
    signalLabel->setText(QString("Signals for %1 (ID: %2)")
                        .arg(entry.messageName.isEmpty() ? "Unknown" : entry.messageName)
                        .arg(entry.messageId));
    
    if (!dbcParser)
    {
        QTreeWidgetItem *item = new QTreeWidgetItem(signalTreeWidget);
        item->setText(0, "No DBC loaded");
        item->setText(1, "N/A");
        return;
    }
    
    // Get message ID
    bool ok;
    uint32_t messageId = entry.messageId.toUInt(&ok, 16);
    if (!ok)
        return;
    
    // Get signals for this message
    QVector<DBCSignal> messageSignals = dbcParser->getSignalsForMessage(messageId);
    
    if (messageSignals.isEmpty())
    {
        QTreeWidgetItem *item = new QTreeWidgetItem(signalTreeWidget);
        item->setText(0, "No signals defined");
        item->setText(1, "N/A");
        return;
    }
    
    // Extract and display signal values
    for (const DBCSignal &signal : messageSignals)
    {
        QTreeWidgetItem *item = new QTreeWidgetItem(signalTreeWidget);
        item->setText(0, signal.name);
        
        if (entry.lastData.size() >= (signal.startBit + signal.length + 7) / 8)
        {
            double value = dbcParser->extractSignalValue(signal, entry.lastData);
            QString formattedValue = dbcParser->formatSignalValue(signal, value);
            item->setText(1, formattedValue);
        }
        else
        {
            item->setText(1, "Insufficient data");
        }
        
        item->setText(2, signal.unit);
        item->setText(3, signal.description);
    }
    
    // Expand all items and resize columns
    signalTreeWidget->expandAll();
    for (int i = 0; i < 4; ++i)
    {
        signalTreeWidget->resizeColumnToContents(i);
    }
}

void FixedPositionTraceWindow::onParsingProgress(int percentage)
{
    progressBar->setValue(percentage);
    statusLabel->setText(QString("Loading ASC file... %1%").arg(percentage));
}

void FixedPositionTraceWindow::onParsingFinished()
{
    progressBar->setVisible(false);
    statusLabel->setText(QString("Loaded successfully. Showing %1 unique message types.")
                        .arg(fixedModel->rowCount(QModelIndex())));
    
    // Auto-resize columns after loading
    for (int i = 0; i < fixedModel->columnCount(QModelIndex()) - 1; ++i)
    {
        logTableView->resizeColumnToContents(i);
    }
}