#pragma once
#include <QMainWindow>
#include <QTableView>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QSortFilterProxyModel>
#include <QHeaderView>
#include <QSplitter>
#include <QTreeWidget>
#include <QProgressBar>
#include "CANLogModel.h"
#include "Parser.h"
#include "DBCParser.h"

class TraceWindow : public QMainWindow
{
    Q_OBJECT
public:
    TraceWindow(const QString &ascPath, const QString &dbcPath, QWidget *parent = nullptr);
    
private slots:
    void onDeltaTimeToggled(bool enabled);
    void onGenerateCSV();
    void onClearAllFilters();
    void onColumnConfiguration();
    void onLogClicked(const QModelIndex &index);
    void onParsingProgress(int percentage);
    void onParsingFinished();
    
private:
    void setupUI();
    void setupToolbar();
    void setupTable();
    void setupSignalView();
    void loadDBC();
    void updateSignalView(int logIndex);
    
    QString ascFilePath;
    QString dbcFilePath;
    
    // UI Components
    QWidget *centralWidget;
    QSplitter *mainSplitter;
    QVBoxLayout *mainLayout;
    QHBoxLayout *toolbarLayout;
    
    // Toolbar buttons
    QPushButton *deltaTimeBtn;
    QPushButton *csvGenerateBtn;
    QPushButton *clearFiltersBtn;
    QPushButton *columnConfigBtn;
    
    // Table components
    QTableView *logTableView;
    CANLogModel *logModel;
    QSortFilterProxyModel *proxyModel;
    
    // Signal view
    QTreeWidget *signalTreeWidget;
    QLabel *signalLabel;
    
    // Progress
    QProgressBar *progressBar;
    QLabel *statusLabel;
    
    // Data
    Parser *parser;
    DBCParser *dbcParser;
    bool deltaTimeEnabled;
};