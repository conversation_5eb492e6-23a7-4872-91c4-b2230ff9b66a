#pragma once
#include <QMainWindow>
#include <QTableView>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QSortFilterProxyModel>
#include <QHeaderView>
#include <QSplitter>
#include <QTreeWidget>
#include <QProgressBar>
#include "FixedPositionModel.h"
#include "Parser.h"
#include "DBCParser.h"

class FixedPositionTraceWindow : public QMainWindow
{
    Q_OBJECT
public:
    FixedPositionTraceWindow(const QString &ascPath, const QString &dbcPath, QWidget *parent = nullptr);
    
private slots:
    void onDeltaTimeToggled(bool enabled);
    void onGenerateCSV();
    void onClearAllFilters();
    void onColumnConfiguration();
    void onLogClicked(const QModelIndex &index);
    void onParsingProgress(int percentage);
    void onParsingFinished();
    void onClearDisplay();
    
private:
    void setupUI();
    void setupToolbar();
    void setupTable();
    void setupSignalView();
    void loadDBC();
    void updateSignalView(int entryIndex);
    
    QString ascFilePath;
    QString dbcFilePath;
    
    // UI Components
    QWidget *centralWidget;
    QSplitter *mainSplitter;
    QVBoxLayout *mainLayout;
    QHBoxLayout *toolbarLayout;
    
    // Toolbar buttons
    QPushButton *deltaTimeBtn;
    QPushButton *csvGenerateBtn;
    QPushButton *clearFiltersBtn;
    QPushButton *columnConfigBtn;
    QPushButton *clearDisplayBtn;
    
    // Table components
    QTableView *logTableView;
    FixedPositionModel *fixedModel;
    QSortFilterProxyModel *proxyModel;
    
    // Signal view
    QTreeWidget *signalTreeWidget;
    QLabel *signalLabel;
    
    // Progress
    QProgressBar *progressBar;
    QLabel *statusLabel;
    
    // Data
    Parser *parser;
    DBCParser *dbcParser;
    bool deltaTimeEnabled;
};