#pragma once
#include <QAbstractTableModel>
#include <QVector>
#include <QMap>
#include <QSet>
#include <QMutex>
#include <QThread>
#include <QTimer>
#include <QAtomicInt>
#include "CANLogModel.h"

struct FixedPositionEntry
{
    QString messageId;
    QString messageName;
    double lastTimestamp;
    int channel;
    QString direction;
    int dlc;
    QVector<uint8_t> lastData;
    int updateCount;
};

// Thread-safe structure for communicating processed results
struct ProcessedBatch
{
    QVector<FixedPositionEntry> newEntries;
    QVector<QPair<int, FixedPositionEntry>> updatedEntries; // row, entry
    QMap<QString, int> newMessageKeyMappings; // messageKey, row
    double baseTimestamp;

    ProcessedBatch() : baseTimestamp(0.0) {}
};

// Forward declaration
class FixedPositionWorker;

class FixedPositionModel : public QAbstractTableModel
{
    Q_OBJECT
public:
    FixedPositionModel(QObject *parent = nullptr);
    ~FixedPositionModel();
    int rowCount(const QModelIndex &) const override;
    int columnCount(const QModelIndex &) const override;
    QVariant data(const QModelIndex &, int role) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role) const override;
    
    void setDeltaTimeEnabled(bool enabled);
    bool isDeltaTimeEnabled() const { return deltaTimeEnabled; }
    
    const FixedPositionEntry& getEntry(int index) const;
    void clear();
    int getRowForMessageKey(const QString &messageKey) const;

public slots:
    void appendLogs(const QVector<CANLog> &batch);

private slots:
    void onBatchProcessed(const ProcessedBatch &result);
    void onUIUpdateTimer();

private:
    void processBatch();
    void addNewEntry(const CANLog &log);
    void updateExistingEntry(int row, const CANLog &log);
    QString createMessageKey(int channel, const QString &messageId) const;
    void initializeWorkerThread();
    void shutdownWorkerThread();
    void applyProcessedBatch(const ProcessedBatch &result);

    // Core data (protected by mutex)
    mutable QMutex dataMutex;
    QVector<FixedPositionEntry> entries;
    QMap<QString, int> messageKeyToRow; // Maps unique message key (channel:ID) to row index
    bool deltaTimeEnabled;
    double baseTimestamp;

    // Threading components
    QThread *workerThread;
    FixedPositionWorker *worker;
    QAtomicInt isShuttingDown;

    // Batch processing optimization
    QVector<CANLog> pendingLogs;
    QSet<int> updatedRows;
    bool batchProcessing;

    // UI update optimization
    QTimer *uiUpdateTimer;
    QVector<ProcessedBatch> pendingUIUpdates;
    QMutex uiUpdateMutex;

    static const int BATCH_SIZE = 100;
    static const int UI_UPDATE_INTERVAL_MS = 16; // ~60 FPS
    static const int MAX_UI_UPDATES_PER_TICK = 5; // Limit updates per timer tick
};

// Worker class for background batch processing
class FixedPositionWorker : public QObject
{
    Q_OBJECT
public:
    explicit FixedPositionWorker(QObject *parent = nullptr);

    // Thread-safe method to set current state
    void setCurrentState(const QVector<FixedPositionEntry> &entries,
                        const QMap<QString, int> &messageKeyToRow,
                        double baseTimestamp);

public slots:
    void processBatch(const QVector<CANLog> &logs);

signals:
    void batchProcessed(const ProcessedBatch &result);

private:
    QString createMessageKey(int channel, const QString &messageId) const;

    // Worker thread's copy of current state
    mutable QMutex stateMutex;
    QVector<FixedPositionEntry> currentEntries;
    QMap<QString, int> currentMessageKeyToRow;
    double currentBaseTimestamp;
};