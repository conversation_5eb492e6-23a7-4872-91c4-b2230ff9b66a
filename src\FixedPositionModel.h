#pragma once
#include <QAbstractTableModel>
#include <QVector>
#include <QHash>
#include <QSet>
#include <QThread>
#include <QTimer>
#include <QMutex>
#include <QQueue>
#include <QAtomicInt>
#include "CANLogModel.h"

struct FixedPositionEntry
{
    QString messageId;
    QString messageName;
    double lastTimestamp;
    int channel;
    QString direction;
    int dlc;
    QVector<uint8_t> lastData;

    // Optimized constructor for faster creation
    FixedPositionEntry() : lastTimestamp(0.0), channel(0), dlc(0) {}
};

// Optimized message key type for better performance
using MessageKey = QPair<int, QString>; // channel, messageId

// Structure for processed results from worker thread
struct ProcessedResult
{
    QVector<FixedPositionEntry> newEntries;
    QHash<MessageKey, int> newKeyMappings;
    QHash<int, FixedPositionEntry> updatedEntries; // row -> entry
    double baseTimestamp;

    ProcessedResult() : baseTimestamp(0.0) {}
};

// Forward declaration
class FixedPositionWorker;

class FixedPositionModel : public QAbstractTableModel
{
    Q_OBJECT
public:
    FixedPositionModel(QObject *parent = nullptr);
    ~FixedPositionModel();
    int rowCount(const QModelIndex &) const override;
    int columnCount(const QModelIndex &) const override;
    QVariant data(const QModelIndex &, int role) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role) const override;
    
    void setDeltaTimeEnabled(bool enabled);
    bool isDeltaTimeEnabled() const { return deltaTimeEnabled; }
    
    const FixedPositionEntry& getEntry(int index) const;
    void clear();
    int getRowForMessageKey(const MessageKey &messageKey) const;

public slots:
    void appendLogs(const QVector<CANLog> &batch);

private slots:
    void onProcessedResult(const ProcessedResult &result);
    void onUIUpdateTimer();

signals:
    void processLogs(const QVector<CANLog> &logs);

private:
    void initializeWorker();
    void shutdownWorker();
    void applyProcessedResult(const ProcessedResult &result);
    void enforceMaxEntries();
    MessageKey createMessageKey(int channel, const QString &messageId) const;

    // Core data - thread-safe access
    mutable QMutex dataMutex;
    QVector<FixedPositionEntry> entries;
    QHash<MessageKey, int> messageKeyToRow;
    bool deltaTimeEnabled;
    double baseTimestamp;

    // Asynchronous processing
    QThread *workerThread;
    FixedPositionWorker *worker;
    QAtomicInt isShuttingDown;

    // UI update management
    QTimer *uiUpdateTimer;
    QQueue<ProcessedResult> pendingResults;
    QMutex resultsMutex;

    // Input queue for worker thread
    QQueue<QVector<CANLog>> inputQueue;
    QMutex inputMutex;

    static const int CHUNK_SIZE = 20; // Small chunks for responsive processing
    static const int MAX_ENTRIES = 4000;
    static const int CLEANUP_THRESHOLD = 4200;
    static const int UI_UPDATE_INTERVAL = 16; // ~60 FPS
};

// Asynchronous worker for processing CAN logs
class FixedPositionWorker : public QObject
{
    Q_OBJECT
public:
    explicit FixedPositionWorker(QObject *parent = nullptr);

    void setCurrentState(const QVector<FixedPositionEntry> &entries,
                        const QHash<MessageKey, int> &keyToRow,
                        double baseTimestamp);

public slots:
    void processLogChunk(const QVector<CANLog> &logs);

signals:
    void resultReady(const ProcessedResult &result);

private:
    MessageKey createMessageKey(int channel, const QString &messageId) const;

    // Worker's copy of current state
    mutable QMutex stateMutex;
    QVector<FixedPositionEntry> currentEntries;
    QHash<MessageKey, int> currentKeyToRow;
    double currentBaseTimestamp;
};