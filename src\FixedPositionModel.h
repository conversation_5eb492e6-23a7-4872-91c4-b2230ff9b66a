#pragma once
#include <QAbstractTableModel>
#include <QVector>
#include <QHash>
#include <QSet>
#include "CANLogModel.h"

struct FixedPositionEntry
{
    QString messageId;
    QString messageName;
    double lastTimestamp;
    int channel;
    QString direction;
    int dlc;
    QVector<uint8_t> lastData;

    // Optimized constructor for faster creation
    FixedPositionEntry() : lastTimestamp(0.0), channel(0), dlc(0) {}
};

// Optimized message key type for better performance
using MessageKey = QPair<int, QString>; // channel, messageId

class FixedPositionModel : public QAbstractTableModel
{
    Q_OBJECT
public:
    FixedPositionModel(QObject *parent = nullptr);
    ~FixedPositionModel();
    int rowCount(const QModelIndex &) const override;
    int columnCount(const QModelIndex &) const override;
    QVariant data(const QModelIndex &, int role) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role) const override;
    
    void setDeltaTimeEnabled(bool enabled);
    bool isDeltaTimeEnabled() const { return deltaTimeEnabled; }
    
    const FixedPositionEntry& getEntry(int index) const;
    void clear();
    int getRowForMessageKey(const MessageKey &messageKey) const;

public slots:
    void appendLogs(const QVector<CANLog> &batch);

private:
    void processBatch();
    void addNewEntry(const CANLog &log);
    void updateExistingEntry(int row, const CANLog &log);
    MessageKey createMessageKey(int channel, const QString &messageId) const;
    void enforceMaxEntries();

    // Core data - simplified and optimized
    QVector<FixedPositionEntry> entries;
    QHash<MessageKey, int> messageKeyToRow; // Hash for O(1) lookup performance
    bool deltaTimeEnabled;
    double baseTimestamp;

    // Batch processing optimization
    QVector<CANLog> pendingLogs;
    QSet<int> updatedRows;
    bool batchProcessing;

    static const int BATCH_SIZE = 50; // Smaller batches for better responsiveness
    static const int MAX_ENTRIES = 4000; // Maximum number of unique messages
    static const int CLEANUP_THRESHOLD = 4200; // Start cleanup when this is reached
};