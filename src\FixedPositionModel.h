#pragma once
#include <QAbstractTableModel>
#include <QVector>
#include <QHash>
#include <QSet>
#include <QTimer>
#include "CANLogModel.h"

struct FixedPositionEntry
{
    QString messageId;
    QString messageName;
    double lastTimestamp;
    int channel;
    QString direction;
    int dlc;
    QVector<uint8_t> lastData;

    // Optimized constructor for faster creation
    FixedPositionEntry() : lastTimestamp(0.0), channel(0), dlc(0) {}
};

// Optimized message key type for better performance
using MessageKey = QPair<int, QString>; // channel, messageId

class FixedPositionModel : public QAbstractTableModel
{
    Q_OBJECT
public:
    FixedPositionModel(QObject *parent = nullptr);
    ~FixedPositionModel();
    int rowCount(const QModelIndex &) const override;
    int columnCount(const QModelIndex &) const override;
    QVariant data(const QModelIndex &, int role) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role) const override;
    
    void setDeltaTimeEnabled(bool enabled);
    bool isDeltaTimeEnabled() const { return deltaTimeEnabled; }
    
    const FixedPositionEntry& getEntry(int index) const;
    void clear();
    int getRowForMessageKey(const MessageKey &messageKey) const;

public slots:
    void appendLogs(const QVector<CANLog> &batch);

private slots:
    void processNextChunk();

private:
    void processChunk(const QVector<CANLog> &chunk);
    void enforceMaxEntries();
    MessageKey createMessageKey(int channel, const QString &messageId) const;

    // Core data - simplified single-threaded access
    QVector<FixedPositionEntry> entries;
    QHash<MessageKey, int> messageKeyToRow;
    bool deltaTimeEnabled;
    double baseTimestamp;

    // Timer-based chunked processing
    QTimer *processingTimer;
    QVector<CANLog> pendingLogs;

    static const int CHUNK_SIZE = 50; // Balanced chunk size for good performance and responsiveness
    static const int MAX_ENTRIES = 4000;
    static const int CLEANUP_THRESHOLD = 4200;
};