#!/bin/bash

cmake -B build -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release .

cmake --build build -j$(nproc)

cd build

# Path to the .exe file
EXE="./CanLogViewer.exe"

# Check if the executable exists
if [[ ! -f "$EXE" ]]; then
    echo "❌ Không tìm thấy $EXE. Vui lòng chạy script từ thư mục chứa CanLogViewer.exe"
    exit 1
fi

DEST_DIR="."  # Destination folder (same as EXE)
QT_PREFIX="/ucrt64"  # Adjust this if needed

echo "📦 Phân tích thư viện phụ thuộc của $EXE..."

# Copy dependent DLLs from ldd output
ldd "$EXE" | awk '{print $3}' | grep -E "$QT_PREFIX|/mingw" | while read -r dll; do
    if [[ -f "$dll" ]]; then
        echo "🔄 Copy: $(basename "$dll")"
        cp -u "$dll" "$DEST_DIR/"
    else
        echo "⚠️ Không tìm thấy: $dll"
    fi
done

# Copy Qt platform plugins
PLUGIN_SRC="/c/msys64/ucrt64/share/qt6/plugins/platforms/qwindows.dll"
PLUGIN_DEST="$DEST_DIR/platforms"

echo "🧩 Kiểm tra Qt platform plugin..."
if [[ -f "$PLUGIN_SRC" ]]; then
    mkdir -p "$PLUGIN_DEST"
    cp -u "$PLUGIN_SRC" "$PLUGIN_DEST/"
    echo "✅ Đã copy plugin: platforms/qwindows.dll"
else
    echo "❌ Không tìm thấy Qt platform plugin: $PLUGIN_SRC"
    echo "👉 Hãy kiểm tra đường dẫn Qt hoặc dùng windeployqt nếu cần"
fi

echo "✅ Hoàn tất copy DLL và plugin."
