#include "TraceWindow.h"
#include <QApplication>
#include <QFileDialog>
#include <QMessageBox>
#include <QTextStream>
#include <QHeaderView>
#include <QTreeWidgetItem>
#include <QStatusBar>
#include <QMenuBar>
#include <QToolBar>
#include <QAction>
#include <QDebug>

TraceWindow::TraceWindow(const QString &ascPath, const QString &dbcPath, QWidget *parent)
    : QMainWindow(parent), ascFilePath(ascPath), dbcFilePath(dbcPath), deltaTimeEnabled(false)
{
    setupUI();
    loadDBC();
    
    // Start parsing ASC file
    parser = new Parser(ascFilePath, logModel, dbcParser);
    connect(parser, &Parser::progressUpdated, this, &TraceWindow::onParsingProgress);
    connect(parser, &Parser::parsingFinished, this, &TraceWindow::onParsingFinished);
    
    statusLabel->setText("Loading ASC file...");
    progressBar->setVisible(true);
    parser->start();
}

void TraceWindow::setupUI()
{
    setWindowTitle("CAN Trace Window");
    resize(1200, 800);
    
    centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);
    
    mainLayout = new QVBoxLayout(centralWidget);
    
    setupToolbar();
    
    // Create main splitter
    mainSplitter = new QSplitter(Qt::Horizontal, this);
    mainLayout->addWidget(mainSplitter);
    
    setupTable();
    setupSignalView();
    
    // Status bar
    statusLabel = new QLabel("Ready", this);
    progressBar = new QProgressBar(this);
    progressBar->setVisible(false);
    
    statusBar()->addWidget(statusLabel);
    statusBar()->addPermanentWidget(progressBar);
}

void TraceWindow::setupToolbar()
{
    QToolBar *toolbar = addToolBar("Main");
    
    // Delta Time button
    deltaTimeBtn = new QPushButton("Delta Time", this);
    deltaTimeBtn->setCheckable(true);
    deltaTimeBtn->setToolTip("Toggle delta time display");
    connect(deltaTimeBtn, &QPushButton::toggled, this, &TraceWindow::onDeltaTimeToggled);
    toolbar->addWidget(deltaTimeBtn);
    
    toolbar->addSeparator();
    
    // CSV Generate button
    csvGenerateBtn = new QPushButton("Generate CSV", this);
    csvGenerateBtn->setToolTip("Export current view to CSV");
    connect(csvGenerateBtn, &QPushButton::clicked, this, &TraceWindow::onGenerateCSV);
    toolbar->addWidget(csvGenerateBtn);
    
    toolbar->addSeparator();
    
    // Clear All Filters button
    clearFiltersBtn = new QPushButton("Clear All Filters", this);
    clearFiltersBtn->setToolTip("Clear all applied filters");
    connect(clearFiltersBtn, &QPushButton::clicked, this, &TraceWindow::onClearAllFilters);
    toolbar->addWidget(clearFiltersBtn);
    
    toolbar->addSeparator();
    
    // Column Configuration button (dummy for now)
    columnConfigBtn = new QPushButton("Column Config", this);
    columnConfigBtn->setToolTip("Configure visible columns (Coming soon)");
    connect(columnConfigBtn, &QPushButton::clicked, this, &TraceWindow::onColumnConfiguration);
    toolbar->addWidget(columnConfigBtn);
}

void TraceWindow::setupTable()
{
    // Create model and proxy for sorting/filtering
    logModel = new CANLogModel(this);
    proxyModel = new QSortFilterProxyModel(this);
    proxyModel->setSourceModel(logModel);
    proxyModel->setFilterCaseSensitivity(Qt::CaseInsensitive);
    
    // Create table view
    logTableView = new QTableView(this);
    logTableView->setModel(proxyModel);
    logTableView->setSortingEnabled(true);
    logTableView->setSelectionBehavior(QAbstractItemView::SelectRows);
    logTableView->setAlternatingRowColors(true);
    
    // Configure headers
    QHeaderView *header = logTableView->horizontalHeader();
    header->setStretchLastSection(true);
    header->setSectionResizeMode(QHeaderView::Interactive);
    
    // Set column widths
    logTableView->setColumnWidth(0, 120); // Timestamp/Delta
    logTableView->setColumnWidth(1, 80);  // Channel
    logTableView->setColumnWidth(2, 100); // ID
    logTableView->setColumnWidth(3, 200); // Message Name
    logTableView->setColumnWidth(4, 80);  // Direction
    logTableView->setColumnWidth(5, 60);  // DLC
    
    // Connect selection signal
    connect(logTableView->selectionModel(), &QItemSelectionModel::currentRowChanged,
            this, &TraceWindow::onLogClicked);
    
    mainSplitter->addWidget(logTableView);
    mainSplitter->setSizes({800, 400});
}

void TraceWindow::setupSignalView()
{
    QWidget *signalWidget = new QWidget(this);
    QVBoxLayout *signalLayout = new QVBoxLayout(signalWidget);
    
    signalLabel = new QLabel("Signal Details", this);
    signalLabel->setStyleSheet("font-weight: bold; padding: 5px;");
    signalLayout->addWidget(signalLabel);
    
    signalTreeWidget = new QTreeWidget(this);
    signalTreeWidget->setHeaderLabels({"Signal Name", "Value", "Unit", "Description"});
    signalTreeWidget->setAlternatingRowColors(true);
    signalLayout->addWidget(signalTreeWidget);
    
    mainSplitter->addWidget(signalWidget);
}

void TraceWindow::loadDBC()
{
    dbcParser = new DBCParser(this);
    if (!dbcParser->loadDBC(dbcFilePath))
    {
        QMessageBox::warning(this, "Warning", 
                           QString("Failed to load DBC file: %1\nSignal parsing will not be available.")
                           .arg(dbcFilePath));
    }
}

void TraceWindow::onDeltaTimeToggled(bool enabled)
{
    deltaTimeEnabled = enabled;
    logModel->setDeltaTimeEnabled(enabled);
    deltaTimeBtn->setText(enabled ? "Absolute Time" : "Delta Time");
}

void TraceWindow::onGenerateCSV()
{
    QString fileName = QFileDialog::getSaveFileName(this, 
                                                   "Export to CSV", 
                                                   "can_log_export.csv", 
                                                   "CSV Files (*.csv)");
    if (fileName.isEmpty())
        return;
    
    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text))
    {
        QMessageBox::critical(this, "Error", "Failed to create CSV file.");
        return;
    }
    
    QTextStream out(&file);
    
    // Write header
    QStringList headers;
    for (int col = 0; col < logModel->columnCount(QModelIndex()); ++col)
    {
        headers << logModel->headerData(col, Qt::Horizontal, Qt::DisplayRole).toString();
    }
    out << headers.join(",") << "\n";
    
    // Write data
    for (int row = 0; row < proxyModel->rowCount(); ++row)
    {
        QStringList rowData;
        for (int col = 0; col < proxyModel->columnCount(); ++col)
        {
            QModelIndex index = proxyModel->index(row, col);
            QString data = proxyModel->data(index, Qt::DisplayRole).toString();
            // Escape commas and quotes in CSV
            if (data.contains(',') || data.contains('"'))
            {
                data = "\"" + data.replace('"', "\"\"") + "\"";
            }
            rowData << data;
        }
        out << rowData.join(",") << "\n";
    }
    
    QMessageBox::information(this, "Export Complete", 
                           QString("Successfully exported %1 rows to %2")
                           .arg(proxyModel->rowCount()).arg(fileName));
}

void TraceWindow::onClearAllFilters()
{
    proxyModel->setFilterRegularExpression(QRegularExpression());
    proxyModel->setFilterKeyColumn(-1);
    statusLabel->setText(QString("Filters cleared. Showing %1 logs.").arg(proxyModel->rowCount()));
}

void TraceWindow::onColumnConfiguration()
{
    QMessageBox::information(this, "Column Configuration", 
                           "Column configuration feature is coming soon!");
}

void TraceWindow::onLogClicked(const QModelIndex &index)
{
    if (!index.isValid())
        return;
    
    // Map proxy index to source index
    QModelIndex sourceIndex = proxyModel->mapToSource(index);
    updateSignalView(sourceIndex.row());
}

void TraceWindow::updateSignalView(int logIndex)
{
    signalTreeWidget->clear();
    
    if (logIndex < 0 || logIndex >= logModel->rowCount(QModelIndex()))
        return;
    
    const CANLog &log = logModel->getLog(logIndex);
    
    // Update label
    signalLabel->setText(QString("Signals for %1 (ID: %2)")
                        .arg(log.messageName.isEmpty() ? "Unknown" : log.messageName)
                        .arg(log.id));
    
    if (!dbcParser)
    {
        QTreeWidgetItem *item = new QTreeWidgetItem(signalTreeWidget);
        item->setText(0, "No DBC loaded");
        item->setText(1, "N/A");
        return;
    }
    
    // Get message ID
    bool ok;
    uint32_t messageId = log.id.toUInt(&ok, 16);
    if (!ok)
        return;
    
    // Get signals for this message
    QVector<DBCSignal> messageSignals = dbcParser->getSignalsForMessage(messageId);
    
    if (messageSignals.isEmpty())
    {
        QTreeWidgetItem *item = new QTreeWidgetItem(signalTreeWidget);
        item->setText(0, "No signals defined");
        item->setText(1, "N/A");
        return;
    }
    
    // Extract and display signal values
    for (const DBCSignal &signal : messageSignals)
    {
        QTreeWidgetItem *item = new QTreeWidgetItem(signalTreeWidget);
        item->setText(0, signal.name);
        
        if (log.data.size() >= (signal.startBit + signal.length + 7) / 8)
        {
            double value = dbcParser->extractSignalValue(signal, log.data);
            QString formattedValue = dbcParser->formatSignalValue(signal, value);
            item->setText(1, formattedValue);
        }
        else
        {
            item->setText(1, "Insufficient data");
        }
        
        item->setText(2, signal.unit);
        item->setText(3, signal.description);
    }
    
    // Expand all items and resize columns
    signalTreeWidget->expandAll();
    for (int i = 0; i < 4; ++i)
    {
        signalTreeWidget->resizeColumnToContents(i);
    }
}

void TraceWindow::onParsingProgress(int percentage)
{
    progressBar->setValue(percentage);
    statusLabel->setText(QString("Loading ASC file... %1%").arg(percentage));
}

void TraceWindow::onParsingFinished()
{
    progressBar->setVisible(false);
    statusLabel->setText(QString("Loaded %1 CAN logs successfully.").arg(logModel->rowCount(QModelIndex())));
    
    // Auto-resize columns after loading
    for (int i = 0; i < logModel->columnCount(QModelIndex()) - 1; ++i)
    {
        logTableView->resizeColumnToContents(i);
    }
}